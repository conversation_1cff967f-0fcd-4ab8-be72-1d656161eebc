<!--
  任务表单组件
  用于创建和编辑任务，包含表单验证、数据提交等功能
  支持创建模式和编辑模式
-->

<template>
  <view class="task-form">
    <!-- 表单头部 -->
    <view class="form-header">
      <text class="form-title">
        {{ mode === 'create' ? '创建任务' : '编辑任务' }}
      </text>
      <button class="close-btn" @click="handleCancel">
        ✕
      </button>
    </view>

    <!-- 表单内容 -->
    <form class="form-content" @submit.prevent="handleSubmit">
      <!-- 任务标题 -->
      <view class="form-group">
        <label class="form-label">任务标题 *</label>
        <input
          class="form-input"
          type="text"
          placeholder="请输入任务标题"
          v-model="formData.title"
          :class="{ 'error': errors.title }"
          @blur="validateTitle"
        />
        <text class="error-message" v-if="errors.title">
          {{ errors.title }}
        </text>
      </view>

      <!-- 任务描述 -->
      <view class="form-group">
        <label class="form-label">任务描述</label>
        <textarea
          class="form-textarea"
          placeholder="请输入任务描述"
          v-model="formData.description"
          :maxlength="500"
          @blur="validateDescription"
        />
        <view class="char-count">
          {{ formData.description.length }}/500
        </view>
      </view>

      <!-- 优先级选择 -->
      <view class="form-group">
        <label class="form-label">优先级 *</label>
        <picker
          :value="priorityIndex"
          :range="priorityOptions"
          range-key="label"
          @change="handlePriorityChange"
        >
          <view class="picker-input" :class="{ 'error': errors.priority }">
            <text class="picker-text">{{ currentPriorityText }}</text>
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
        <text class="error-message" v-if="errors.priority">
          {{ errors.priority }}
        </text>
      </view>

      <!-- 截止日期 -->
      <view class="form-group">
        <label class="form-label">截止日期</label>
        <picker
          mode="date"
          :value="dueDateString"
          @change="handleDateChange"
        >
          <view class="picker-input">
            <text class="picker-text">
              {{ dueDateString || '请选择截止日期' }}
            </text>
            <text class="picker-arrow">📅</text>
          </view>
        </picker>
        <button 
          class="clear-date-btn" 
          v-if="formData.dueDate"
          @click="clearDueDate"
          type="button"
        >
          清除日期
        </button>
      </view>

      <!-- 标签输入 -->
      <view class="form-group">
        <label class="form-label">标签</label>
        <view class="tags-input-container">
          <!-- 已添加的标签 -->
          <view class="tags-list" v-if="formData.tags.length > 0">
            <view 
              class="tag-item" 
              v-for="(tag, index) in formData.tags" 
              :key="index"
            >
              <text class="tag-text">{{ tag }}</text>
              <button 
                class="tag-remove" 
                @click="removeTag(index)"
                type="button"
              >
                ✕
              </button>
            </view>
          </view>
          
          <!-- 标签输入框 -->
          <view class="tag-input-row">
            <input
              class="tag-input"
              type="text"
              placeholder="输入标签后按回车添加"
              v-model="newTag"
              @confirm="addTag"
              @blur="addTag"
            />
            <button 
              class="add-tag-btn" 
              @click="addTag"
              type="button"
            >
              添加
            </button>
          </view>
        </view>
        <text class="form-hint">
          最多可添加5个标签，每个标签不超过10个字符
        </text>
      </view>

      <!-- 表单操作按钮 -->
      <view class="form-actions">
        <button 
          class="btn btn-cancel" 
          @click="handleCancel"
          type="button"
        >
          取消
        </button>
        
        <button 
          class="btn btn-submit" 
          :disabled="!isFormValid || loading"
          type="submit"
        >
          {{ loading ? '保存中...' : (mode === 'create' ? '创建任务' : '保存修改') }}
        </button>
        
        <!-- 删除按钮（仅编辑模式显示） -->
        <button 
          class="btn btn-delete" 
          v-if="mode === 'edit' && task"
          @click="handleDelete"
          type="button"
        >
          删除任务
        </button>
      </view>
    </form>
  </view>
</template>

<script setup lang="ts">
/**
 * 任务表单组件的逻辑部分
 * 处理表单数据、验证、提交等功能
 */

import { ref, reactive, computed, watch, onMounted } from 'vue'
import type { Task, TaskPriority, CreateTaskData, UpdateTaskData } from '@/types'
import { formatDate } from '@/utils'

// 定义组件接收的属性
interface Props {
  task?: Task                      // 编辑时的任务数据
  mode: 'create' | 'edit'         // 表单模式
  loading?: boolean               // 加载状态
}

// 定义组件发出的事件
interface Emits {
  (e: 'submit', data: CreateTaskData | UpdateTaskData): void    // 表单提交事件
  (e: 'cancel'): void                                          // 取消事件
  (e: 'delete', taskId: string): void                         // 删除事件
}

// 接收属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  loading: false
})

// 定义事件发射器
const emit = defineEmits<Emits>()

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  priority: 'medium' as TaskPriority,
  dueDate: null as Date | null,
  tags: [] as string[]
})

// 表单验证错误
const errors = reactive({
  title: '',
  description: '',
  priority: ''
})

// 新标签输入
const newTag = ref('')

// 优先级选项
const priorityOptions = [
  { value: 'low', label: '低优先级' },
  { value: 'medium', label: '中优先级' },
  { value: 'high', label: '高优先级' }
]

// 计算属性：当前优先级索引
const priorityIndex = computed(() => {
  return priorityOptions.findIndex(option => option.value === formData.priority)
})

// 计算属性：当前优先级文本
const currentPriorityText = computed(() => {
  const option = priorityOptions[priorityIndex.value]
  return option ? option.label : '请选择优先级'
})

// 计算属性：截止日期字符串
const dueDateString = computed(() => {
  return formData.dueDate ? formatDate(formData.dueDate, 'YYYY-MM-DD') : ''
})

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return formData.title.trim() !== '' && 
         formData.priority !== '' && 
         !errors.title && 
         !errors.description && 
         !errors.priority
})

/**
 * 初始化表单数据
 */
const initFormData = (): void => {
  if (props.mode === 'edit' && props.task) {
    formData.title = props.task.title
    formData.description = props.task.description
    formData.priority = props.task.priority
    formData.dueDate = props.task.dueDate || null
    formData.tags = [...props.task.tags]
  } else {
    // 创建模式，重置表单
    formData.title = ''
    formData.description = ''
    formData.priority = 'medium'
    formData.dueDate = null
    formData.tags = []
  }
  
  // 清除错误信息
  errors.title = ''
  errors.description = ''
  errors.priority = ''
}

/**
 * 验证标题
 */
const validateTitle = (): void => {
  const title = formData.title.trim()
  if (!title) {
    errors.title = '请输入任务标题'
  } else if (title.length > 100) {
    errors.title = '任务标题不能超过100个字符'
  } else {
    errors.title = ''
  }
}

/**
 * 验证描述
 */
const validateDescription = (): void => {
  if (formData.description.length > 500) {
    errors.description = '任务描述不能超过500个字符'
  } else {
    errors.description = ''
  }
}

/**
 * 验证优先级
 */
const validatePriority = (): void => {
  if (!formData.priority) {
    errors.priority = '请选择任务优先级'
  } else {
    errors.priority = ''
  }
}

/**
 * 处理优先级改变
 */
const handlePriorityChange = (event: any): void => {
  const index = event.detail.value
  const selectedOption = priorityOptions[index]
  formData.priority = selectedOption.value as TaskPriority
  validatePriority()
}

/**
 * 处理日期改变
 */
const handleDateChange = (event: any): void => {
  const dateString = event.detail.value
  formData.dueDate = dateString ? new Date(dateString) : null
}

/**
 * 清除截止日期
 */
const clearDueDate = (): void => {
  formData.dueDate = null
}

/**
 * 添加标签
 */
const addTag = (): void => {
  const tag = newTag.value.trim()
  
  if (!tag) return
  
  if (tag.length > 10) {
    uni.showToast({
      title: '标签不能超过10个字符',
      icon: 'none'
    })
    return
  }
  
  if (formData.tags.length >= 5) {
    uni.showToast({
      title: '最多只能添加5个标签',
      icon: 'none'
    })
    return
  }
  
  if (formData.tags.includes(tag)) {
    uni.showToast({
      title: '标签已存在',
      icon: 'none'
    })
    return
  }
  
  formData.tags.push(tag)
  newTag.value = ''
}

/**
 * 移除标签
 */
const removeTag = (index: number): void => {
  formData.tags.splice(index, 1)
}

/**
 * 处理表单提交
 */
const handleSubmit = (): void => {
  // 验证所有字段
  validateTitle()
  validateDescription()
  validatePriority()
  
  if (!isFormValid.value) {
    uni.showToast({
      title: '请检查表单输入',
      icon: 'none'
    })
    return
  }
  
  // 构建提交数据
  const submitData = {
    title: formData.title.trim(),
    description: formData.description.trim(),
    priority: formData.priority,
    dueDate: formData.dueDate,
    tags: formData.tags
  }
  
  emit('submit', submitData)
}

/**
 * 处理取消
 */
const handleCancel = (): void => {
  emit('cancel')
}

/**
 * 处理删除
 */
const handleDelete = (): void => {
  if (!props.task) return
  
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这个任务吗？此操作不可恢复。',
    success: (res) => {
      if (res.confirm && props.task) {
        emit('delete', props.task.id)
      }
    }
  })
}

// 监听模式和任务数据变化，重新初始化表单
watch([() => props.mode, () => props.task], () => {
  initFormData()
}, { immediate: true })

// 组件挂载时初始化表单
onMounted(() => {
  initFormData()
})
</script>

<style lang="scss" scoped>
/**
 * 任务表单组件的样式
 */

.task-form {
  background-color: var(--bg-primary);
  border-radius: var(--radius-large);
  box-shadow: var(--shadow-medium);
  overflow: hidden;
  max-width: 600px;
  margin: 0 auto;
}

/* 表单头部 */
.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--text-white);
}

.form-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  border-radius: var(--radius-round);
  font-size: var(--font-size-lg);
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }
}

/* 表单内容 */
.form-content {
  padding: var(--spacing-lg);
}

.form-group {
  margin-bottom: var(--spacing-lg);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  background-color: var(--bg-primary);
  transition: border-color var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }

  &.error {
    border-color: var(--error-color);
  }

  &::placeholder {
    color: var(--text-tertiary);
  }
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.char-count {
  text-align: right;
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

.error-message {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--error-color);
  margin-top: var(--spacing-xs);
}

.form-hint {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-top: var(--spacing-xs);
}

/* 选择器样式 */
.picker-input {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  background-color: var(--bg-primary);
  cursor: pointer;
  transition: border-color var(--transition-fast);

  &:hover {
    border-color: var(--primary-color);
  }

  &.error {
    border-color: var(--error-color);
  }
}

.picker-text {
  font-size: var(--font-size-md);
  color: var(--text-primary);
}

.picker-arrow {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.clear-date-btn {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: transparent;
  color: var(--error-color);
  border: 1px solid var(--error-color);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--error-color);
    color: var(--text-white);
  }
}

/* 标签输入 */
.tags-input-container {
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: var(--spacing-sm);
  background-color: var(--bg-primary);
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.tag-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-color);
  color: var(--text-white);
  border-radius: var(--radius-large);
  font-size: var(--font-size-xs);
}

.tag-text {
  margin-right: var(--spacing-xs);
}

.tag-remove {
  width: 16px;
  height: 16px;
  border: none;
  background-color: rgba(255, 255, 255, 0.3);
  color: var(--text-white);
  border-radius: var(--radius-round);
  font-size: 10px;
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    background-color: rgba(255, 255, 255, 0.5);
  }
}

.tag-input-row {
  display: flex;
  gap: var(--spacing-sm);
}

.tag-input {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  background-color: transparent;
  font-size: var(--font-size-sm);

  &:focus {
    outline: none;
  }
}

.add-tag-btn {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }
}

/* 表单操作按钮 */
.form-actions {
  display: flex;
  gap: var(--spacing-md);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.btn {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:hover:not(:disabled) {
    opacity: 0.8;
  }

  &:active:not(:disabled) {
    transform: scale(0.98);
  }
}

.btn-cancel {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.btn-submit {
  background-color: var(--primary-color);
  color: var(--text-white);
}

.btn-delete {
  background-color: var(--error-color);
  color: var(--text-white);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-form {
    margin: var(--spacing-sm);
    border-radius: var(--radius-medium);
  }

  .form-content {
    padding: var(--spacing-md);
  }

  .form-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .tags-list {
    gap: var(--spacing-xs);
  }

  .tag-input-row {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}
</style>

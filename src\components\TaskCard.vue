<!--
  任务卡片组件
  用于显示单个任务的信息，包括标题、描述、状态、优先级等
  支持点击、状态切换、删除等操作
-->

<template>
  <view class="task-card" @click="handleCardClick">
    <!-- 任务主要信息区域 -->
    <view class="task-header">
      <!-- 任务标题和状态 -->
      <view class="task-title-row">
        <text class="task-title" :class="{ 'completed': task.status === 'completed' }">
          {{ task.title }}
        </text>
        <view class="task-status" :class="`status-${task.status}`">
          {{ getStatusText(task.status) }}
        </view>
      </view>
      
      <!-- 任务描述 -->
      <text class="task-description" v-if="task.description">
        {{ task.description }}
      </text>
    </view>

    <!-- 任务元信息区域 -->
    <view class="task-meta">
      <!-- 优先级标签 -->
      <view class="priority-tag" :class="`priority-${task.priority}`">
        <text class="priority-text">{{ getPriorityText(task.priority) }}</text>
      </view>

      <!-- 截止日期 -->
      <view class="due-date" v-if="task.dueDate">
        <text class="due-date-text" :class="{ 'overdue': isOverdue }">
          {{ formatDate(task.dueDate, 'MM-DD') }}
        </text>
      </view>

      <!-- 创建时间 -->
      <text class="created-time">
        {{ getRelativeTime(task.createdAt) }}
      </text>
    </view>

    <!-- 标签区域 -->
    <view class="task-tags" v-if="task.tags.length > 0">
      <view class="tag" v-for="tag in task.tags" :key="tag">
        <text class="tag-text">{{ tag }}</text>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="task-actions" v-if="showActions">
      <!-- 状态切换按钮 -->
      <button 
        class="action-btn status-btn" 
        :class="`btn-${task.status}`"
        @click.stop="handleStatusToggle"
      >
        {{ getNextStatusText() }}
      </button>

      <!-- 编辑按钮 -->
      <button class="action-btn edit-btn" @click.stop="handleEdit">
        编辑
      </button>

      <!-- 删除按钮 -->
      <button class="action-btn delete-btn" @click.stop="handleDelete">
        删除
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 任务卡片组件的逻辑部分
 * 使用Vue3的Composition API和TypeScript
 */

import { computed } from 'vue'
import type { Task, TaskStatus } from '@/types'
import { getStatusText, getPriorityText, formatDate, getRelativeTime } from '@/utils'

// 定义组件接收的属性
interface Props {
  task: Task                    // 任务数据
  showActions?: boolean         // 是否显示操作按钮
  compact?: boolean            // 是否使用紧凑模式
}

// 定义组件发出的事件
interface Emits {
  (e: 'task-click', task: Task): void                           // 任务卡片点击事件
  (e: 'status-change', taskId: string, status: TaskStatus): void // 状态改变事件
  (e: 'edit-task', task: Task): void                           // 编辑任务事件
  (e: 'delete-task', taskId: string): void                     // 删除任务事件
}

// 接收属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})

// 定义事件发射器
const emit = defineEmits<Emits>()

/**
 * 计算属性：判断任务是否已过期
 * 如果有截止日期且当前时间已超过截止日期，则认为已过期
 */
const isOverdue = computed(() => {
  if (!props.task.dueDate) return false
  return new Date() > props.task.dueDate && props.task.status !== 'completed'
})

/**
 * 获取下一个状态的文本
 * 用于状态切换按钮的显示
 */
const getNextStatusText = (): string => {
  switch (props.task.status) {
    case 'pending':
      return '开始'
    case 'in_progress':
      return '完成'
    case 'completed':
      return '重新开始'
    default:
      return '开始'
  }
}

/**
 * 处理卡片点击事件
 * 向父组件发送任务点击事件
 */
const handleCardClick = (): void => {
  emit('task-click', props.task)
}

/**
 * 处理状态切换事件
 * 计算下一个状态并发送状态改变事件
 */
const handleStatusToggle = (): void => {
  let nextStatus: TaskStatus
  
  switch (props.task.status) {
    case 'pending':
      nextStatus = 'in_progress'
      break
    case 'in_progress':
      nextStatus = 'completed'
      break
    case 'completed':
      nextStatus = 'pending'
      break
    default:
      nextStatus = 'pending'
  }
  
  emit('status-change', props.task.id, nextStatus)
}

/**
 * 处理编辑事件
 * 向父组件发送编辑任务事件
 */
const handleEdit = (): void => {
  emit('edit-task', props.task)
}

/**
 * 处理删除事件
 * 向父组件发送删除任务事件
 */
const handleDelete = (): void => {
  // 可以在这里添加确认对话框
  emit('delete-task', props.task.id)
}
</script>

<style lang="scss" scoped>
/**
 * 任务卡片组件的样式
 * 使用SCSS语法和CSS变量
 */

.task-card {
  background-color: var(--bg-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  transition: all var(--transition-fast);
  cursor: pointer;

  // 悬停效果
  &:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
  }

  // 激活效果
  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-light);
  }
}

/* 任务头部区域 */
.task-header {
  margin-bottom: var(--spacing-sm);
}

.task-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.task-title {
  flex: 1;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-right: var(--spacing-sm);

  // 已完成任务的样式
  &.completed {
    text-decoration: line-through;
    color: var(--text-secondary);
  }
}

.task-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 任务状态标签 */
.task-status {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-large);
  font-size: var(--font-size-xs);
  font-weight: 500;
  white-space: nowrap;

  &.status-pending {
    background-color: rgba(255, 149, 0, 0.1);
    color: #FF9500;
  }

  &.status-in_progress {
    background-color: rgba(0, 122, 255, 0.1);
    color: #007AFF;
  }

  &.status-completed {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34C759;
  }
}

/* 任务元信息区域 */
.task-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.priority-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;

  &.priority-low {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34C759;
  }

  &.priority-medium {
    background-color: rgba(255, 149, 0, 0.1);
    color: #FF9500;
  }

  &.priority-high {
    background-color: rgba(255, 59, 48, 0.1);
    color: #FF3B30;
  }
}

.priority-text {
  font-size: var(--font-size-xs);
}

.due-date {
  display: flex;
  align-items: center;
}

.due-date-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);

  &.overdue {
    color: var(--error-color);
    font-weight: 600;
  }
}

.created-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-left: auto;
}

/* 标签区域 */
.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-sm);
}

.tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-large);
}

.tag-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
}

/* 操作按钮区域 */
.task-actions {
  display: flex;
  gap: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-light);
}

.action-btn {
  flex: 1;
  padding: var(--spacing-xs) var(--spacing-sm);
  border: none;
  border-radius: var(--radius-small);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.98);
  }
}

.status-btn {
  background-color: var(--primary-color);
  color: var(--text-white);

  &.btn-pending {
    background-color: #FF9500;
  }

  &.btn-in_progress {
    background-color: #007AFF;
  }

  &.btn-completed {
    background-color: #34C759;
  }
}

.edit-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.delete-btn {
  background-color: transparent;
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

/* 紧凑模式样式 */
.task-card.compact {
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);

  .task-title {
    font-size: var(--font-size-md);
  }

  .task-description {
    -webkit-line-clamp: 1;
  }

  .task-actions {
    display: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-card {
    margin: var(--spacing-sm);
    padding: var(--spacing-sm);
  }

  .task-title {
    font-size: var(--font-size-md);
  }

  .task-actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .action-btn {
    padding: var(--spacing-sm);
  }
}
</style>

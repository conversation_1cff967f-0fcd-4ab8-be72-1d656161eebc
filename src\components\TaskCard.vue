<!--
  任务卡片组件
  用于显示单个任务的信息，包括标题、描述、状态、优先级等
  支持点击、状态切换、删除等操作
-->

<template>
  <view class="task-card" @click="handleCardClick">
    <!-- 任务主要信息区域 -->
    <view class="task-header">
      <!-- 任务标题和状态 -->
      <view class="task-title-row">
        <text class="task-title" :class="{ 'completed': task.status === 'completed' }">
          {{ task.title }}
        </text>
        <view class="task-status" :class="`status-${task.status}`">
          {{ getStatusText(task.status) }}
        </view>
      </view>
      
      <!-- 任务描述 -->
      <text class="task-description" v-if="task.description">
        {{ task.description }}
      </text>
    </view>

    <!-- 任务元信息区域 -->
    <view class="task-meta">
      <!-- 优先级标签 -->
      <view class="priority-tag" :class="`priority-${task.priority}`">
        <text class="priority-text">{{ getPriorityText(task.priority) }}</text>
      </view>

      <!-- 截止日期 -->
      <view class="due-date" v-if="task.dueDate">
        <text class="due-date-text" :class="{ 'overdue': isOverdue }">
          {{ formatDate(task.dueDate, 'MM-DD') }}
        </text>
      </view>

      <!-- 创建时间 -->
      <text class="created-time">
        {{ getRelativeTime(task.createdAt) }}
      </text>
    </view>

    <!-- 标签区域 -->
    <view class="task-tags" v-if="task.tags.length > 0">
      <view class="tag" v-for="tag in task.tags" :key="tag">
        <text class="tag-text">{{ tag }}</text>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="task-actions" v-if="showActions">
      <!-- 状态切换按钮 -->
      <button 
        class="action-btn status-btn" 
        :class="`btn-${task.status}`"
        @click.stop="handleStatusToggle"
      >
        {{ getNextStatusText() }}
      </button>

      <!-- 编辑按钮 -->
      <button class="action-btn edit-btn" @click.stop="handleEdit">
        编辑
      </button>

      <!-- 删除按钮 -->
      <button class="action-btn delete-btn" @click.stop="handleDelete">
        删除
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 任务卡片组件的逻辑部分
 * 使用Vue3的Composition API和TypeScript
 */

import { computed } from 'vue'
import type { Task } from '@/types'
import { TaskStatus } from '@/types'
import { getStatusText, getPriorityText, formatDate, getRelativeTime } from '@/utils'

// 定义组件接收的属性
interface Props {
  task: Task                    // 任务数据
  showActions?: boolean         // 是否显示操作按钮
  compact?: boolean            // 是否使用紧凑模式
}

// 定义组件发出的事件
interface Emits {
  (e: 'task-click', task: Task): void                           // 任务卡片点击事件
  (e: 'status-change', taskId: string, status: TaskStatus): void // 状态改变事件
  (e: 'edit-task', task: Task): void                           // 编辑任务事件
  (e: 'delete-task', taskId: string): void                     // 删除任务事件
}

// 接收属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})

// 定义事件发射器
const emit = defineEmits<Emits>()

/**
 * 计算属性：判断任务是否已过期
 * 如果有截止日期且当前时间已超过截止日期，则认为已过期
 */
const isOverdue = computed(() => {
  if (!props.task.dueDate) return false
  return new Date() > props.task.dueDate && props.task.status !== 'completed'
})

/**
 * 获取下一个状态的文本
 * 用于状态切换按钮的显示
 */
const getNextStatusText = (): string => {
  switch (props.task.status) {
    case 'pending':
      return '开始'
    case 'in_progress':
      return '完成'
    case 'completed':
      return '重新开始'
    default:
      return '开始'
  }
}

/**
 * 处理卡片点击事件
 * 向父组件发送任务点击事件
 */
const handleCardClick = (): void => {
  emit('task-click', props.task)
}

/**
 * 处理状态切换事件
 * 计算下一个状态并发送状态改变事件
 */
const handleStatusToggle = (): void => {
  let nextStatus: TaskStatus

  switch (props.task.status) {
    case TaskStatus.PENDING:
      nextStatus = TaskStatus.IN_PROGRESS
      break
    case TaskStatus.IN_PROGRESS:
      nextStatus = TaskStatus.COMPLETED
      break
    case TaskStatus.COMPLETED:
      nextStatus = TaskStatus.PENDING
      break
    default:
      nextStatus = TaskStatus.PENDING
  }

  emit('status-change', props.task.id, nextStatus)
}

/**
 * 处理编辑事件
 * 向父组件发送编辑任务事件
 */
const handleEdit = (): void => {
  emit('edit-task', props.task)
}

/**
 * 处理删除事件
 * 向父组件发送删除任务事件
 */
const handleDelete = (): void => {
  // 可以在这里添加确认对话框
  emit('delete-task', props.task.id)
}
</script>

<style lang="scss" scoped>
// 任务卡片组件的样式
// 使用SCSS语法和变量

.task-card {
  @include card-style;
  margin-bottom: $spacing-md;
  transition: all $transition-fast;
  cursor: pointer;

  &:hover {
    box-shadow: $shadow-medium;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
    box-shadow: $shadow-light;
  }
}

// 任务头部区域
.task-header {
  margin-bottom: $spacing-sm;
}

.task-title-row {
  @include flex(row, space-between, flex-start);
  margin-bottom: $spacing-xs;
}

.task-title {
  flex: 1;
  font-size: $font-size-lg;
  font-weight: 600;
  color: $text-primary;
  line-height: $line-height-tight;
  margin-right: $spacing-sm;

  &.completed {
    text-decoration: line-through;
    color: $text-secondary;
  }
}

.task-description {
  font-size: $font-size-sm;
  color: $text-secondary;
  line-height: $line-height-normal;
  @include text-ellipsis(2);
}

// 任务状态标签
.task-status {
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-large;
  font-size: $font-size-xs;
  font-weight: 500;
  white-space: nowrap;

  &.status-pending {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
  }

  &.status-in_progress {
    background-color: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  &.status-completed {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }
}

// 任务元信息区域
.task-meta {
  @include flex(row, flex-start, center);
  gap: $spacing-sm;
  margin-bottom: $spacing-sm;
}

.priority-tag {
  padding: $spacing-xs $spacing-sm;
  border-radius: $radius-small;
  font-size: $font-size-xs;
  font-weight: 500;

  &.priority-low {
    background-color: rgba($success-color, 0.1);
    color: $success-color;
  }

  &.priority-medium {
    background-color: rgba($warning-color, 0.1);
    color: $warning-color;
  }

  &.priority-high {
    background-color: rgba($error-color, 0.1);
    color: $error-color;
  }
}

.priority-text {
  font-size: $font-size-xs;
}

.due-date {
  @include flex(row, flex-start, center);
}

.due-date-text {
  font-size: $font-size-xs;
  color: $text-secondary;

  &.overdue {
    color: $error-color;
    font-weight: 600;
  }
}

.created-time {
  font-size: $font-size-xs;
  color: $text-tertiary;
  margin-left: auto;
}

// 标签区域
.task-tags {
  @include flex(row, flex-start, center);
  flex-wrap: wrap;
  gap: $spacing-xs;
  margin-bottom: $spacing-sm;
}

.tag {
  padding: $spacing-xs $spacing-sm;
  background-color: $bg-secondary;
  border-radius: $radius-large;
}

.tag-text {
  font-size: $font-size-xs;
  color: $text-secondary;
}

// 操作按钮区域
.task-actions {
  @include flex(row, flex-start, center);
  gap: $spacing-sm;
  padding-top: $spacing-sm;
  border-top: 1px solid $border-light;
}

.action-btn {
  flex: 1;
  padding: $spacing-xs $spacing-sm;
  border: none;
  border-radius: $radius-small;
  font-size: $font-size-sm;
  font-weight: 500;
  cursor: pointer;
  transition: all $transition-fast;

  &:hover {
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.98);
  }
}

.status-btn {
  background-color: $primary-color;
  color: $text-white;

  &.btn-pending {
    background-color: $warning-color;
  }

  &.btn-in_progress {
    background-color: $primary-color;
  }

  &.btn-completed {
    background-color: $success-color;
  }
}

.edit-btn {
  background-color: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;
}

.delete-btn {
  background-color: transparent;
  color: $error-color;
  border: 1px solid $error-color;
}

// 紧凑模式样式
.task-card.compact {
  padding: $spacing-sm;
  margin-bottom: $spacing-sm;

  .task-title {
    font-size: $font-size-md;
  }

  .task-description {
    @include text-ellipsis(1);
  }

  .task-actions {
    display: none;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .task-card {
    margin: $spacing-sm;
    padding: $spacing-sm;
  }

  .task-title {
    font-size: $font-size-md;
  }

  .task-actions {
    flex-direction: column;
    gap: $spacing-xs;
  }

  .action-btn {
    padding: $spacing-sm;
  }
}
</style>

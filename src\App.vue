<!--
  应用主组件
  这是UniApp应用的根组件，负责全局配置和样式
-->

<script setup lang="ts">
/**
 * 应用主组件的逻辑部分
 * 处理应用级别的生命周期和全局配置
 */

import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";

/**
 * 应用启动时的初始化
 */
onLaunch(() => {
  console.log("任务管理应用启动");

  // 这里可以进行全局初始化操作
  // 例如：检查登录状态、初始化全局配置等
  initApp();
});

/**
 * 应用显示时的处理
 */
onShow(() => {
  console.log("任务管理应用显示");

  // 这里可以处理应用从后台切换到前台的逻辑
  // 例如：刷新数据、检查更新等
});

/**
 * 应用隐藏时的处理
 */
onHide(() => {
  console.log("任务管理应用隐藏");

  // 这里可以处理应用切换到后台的逻辑
  // 例如：保存数据、暂停定时器等
});

/**
 * 初始化应用
 */
const initApp = (): void => {
  // 设置全局错误处理
  setupGlobalErrorHandler();

  // 设置全局请求拦截器
  setupRequestInterceptor();

  // 初始化主题
  initTheme();
};

/**
 * 设置全局错误处理
 */
const setupGlobalErrorHandler = (): void => {
  // 监听未捕获的错误
  uni.onError((error) => {
    console.error("全局错误:", error);
    // 这里可以上报错误到服务器
  });
};

/**
 * 设置全局请求拦截器
 */
const setupRequestInterceptor = (): void => {
  // 这里可以设置全局的请求拦截器
  // 例如：添加token、处理响应错误等
};

/**
 * 初始化主题
 */
const initTheme = (): void => {
  // 这里可以根据用户偏好设置主题
  // 例如：深色模式、浅色模式等
};
</script>

<style lang="scss">
/**
 * 全局样式
 * 这里的样式会应用到整个应用
 */

/* 引入通用样式 */
@import '@/styles/common.scss';

/* 全局重置样式 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

/* 页面基础样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--text-tertiary);
}

/* 全局动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 动画工具类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

.pulse {
  animation: pulse 0.3s ease-in-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --primary-color: #0A84FF;
    --text-primary: #FFFFFF;
    --text-secondary: #EBEBF5;
    --text-tertiary: #EBEBF5;
    --bg-primary: #1C1C1E;
    --bg-secondary: #000000;
    --border-color: #38383A;
    --border-light: #38383A;
  }
}
</style>

/**
 * 任务管理应用的类型定义文件
 * 这个文件定义了整个应用中使用的所有数据类型和接口
 * 使用TypeScript可以提供更好的代码提示和类型检查
 */

// 任务状态枚举
// 枚举类型可以限制变量只能取特定的值，提高代码的安全性
export enum TaskStatus {
  PENDING = 'pending',     // 待完成
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed'  // 已完成
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 'low',       // 低优先级
  MEDIUM = 'medium', // 中优先级
  HIGH = 'high'      // 高优先级
}

// 任务接口定义
// 接口定义了对象应该具有的属性和方法
export interface Task {
  id: string;                    // 任务唯一标识符
  title: string;                 // 任务标题
  description: string;           // 任务描述
  status: TaskStatus;            // 任务状态
  priority: TaskPriority;        // 任务优先级
  createdAt: Date;              // 创建时间
  updatedAt: Date;              // 更新时间
  dueDate?: Date;               // 截止日期（可选）
  tags: string[];               // 标签数组
}

// 任务创建时的数据接口
// 创建任务时不需要id、创建时间等自动生成的字段
export interface CreateTaskData {
  title: string;
  description: string;
  priority: TaskPriority;
  dueDate?: Date;
  tags: string[];
}

// 任务更新时的数据接口
// 更新时所有字段都是可选的，只更新传入的字段
export interface UpdateTaskData {
  title?: string;
  description?: string;
  status?: TaskStatus;
  priority?: TaskPriority;
  dueDate?: Date;
  tags?: string[];
}

// 任务统计信息接口
export interface TaskStats {
  total: number;        // 总任务数
  pending: number;      // 待完成任务数
  inProgress: number;   // 进行中任务数
  completed: number;    // 已完成任务数
}

// 组件事件接口定义
// 定义组件之间传递的事件数据结构

// 任务操作事件
export interface TaskActionEvent {
  type: 'create' | 'update' | 'delete' | 'toggle-status';
  taskId?: string;
  taskData?: CreateTaskData | UpdateTaskData;
}

// 筛选条件接口
export interface TaskFilter {
  status?: TaskStatus;
  priority?: TaskPriority;
  searchText?: string;
  tags?: string[];
}

// 排序选项接口
export interface SortOption {
  field: 'createdAt' | 'updatedAt' | 'dueDate' | 'priority' | 'title';
  order: 'asc' | 'desc';
}

// 页面导航参数接口
export interface NavigationParams {
  taskId?: string;
  mode?: 'view' | 'edit' | 'create';
}

// API响应接口
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// 用户偏好设置接口
export interface UserPreferences {
  theme: 'light' | 'dark';
  defaultPriority: TaskPriority;
  autoSave: boolean;
  notifications: boolean;
}

// 组件Props接口定义
// 定义各个组件接收的属性类型

// 任务卡片组件Props
export interface TaskCardProps {
  task: Task;
  showActions?: boolean;
  compact?: boolean;
}

// 任务列表组件Props
export interface TaskListProps {
  tasks: Task[];
  filter?: TaskFilter;
  sortOption?: SortOption;
  loading?: boolean;
}

// 任务表单组件Props
export interface TaskFormProps {
  task?: Task;
  mode: 'create' | 'edit';
  loading?: boolean;
}

// 统计卡片组件Props
export interface StatsCardProps {
  stats: TaskStats;
  showDetails?: boolean;
}

// 筛选器组件Props
export interface FilterProps {
  currentFilter: TaskFilter;
  availableTags: string[];
}

// 组件Emits接口定义
// 定义组件向父组件发送的事件

// 任务卡片组件事件
export interface TaskCardEmits {
  'task-click': [task: Task];
  'status-change': [taskId: string, status: TaskStatus];
  'delete-task': [taskId: string];
  'edit-task': [task: Task];
}

// 任务列表组件事件
export interface TaskListEmits {
  'task-select': [task: Task];
  'filter-change': [filter: TaskFilter];
  'sort-change': [sortOption: SortOption];
  'load-more': [];
}

// 任务表单组件事件
export interface TaskFormEmits {
  'submit': [taskData: CreateTaskData | UpdateTaskData];
  'cancel': [];
  'delete': [taskId: string];
}

// 筛选器组件事件
export interface FilterEmits {
  'filter-change': [filter: TaskFilter];
  'reset-filter': [];
}

// 工具类型定义
// 一些常用的工具类型

// 分页数据类型
export interface PaginatedData<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

// 加载状态类型
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// 主题类型
export type ThemeType = 'light' | 'dark';

// 设备类型
export type DeviceType = 'mobile' | 'tablet' | 'desktop';

<!--
  任务详情页面
  显示单个任务的详细信息，支持编辑和删除操作
  展示任务的所有属性和操作历史
-->

<template>
  <view class="task-detail-page">
    <!-- 加载状态 -->
    <view class="loading-container" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 任务不存在 -->
    <view class="not-found-container" v-else-if="!task">
      <text class="not-found-title">任务不存在</text>
      <text class="not-found-description">该任务可能已被删除或不存在</text>
      <button class="back-btn" @click="goBack">返回列表</button>
    </view>

    <!-- 任务详情内容 -->
    <view class="detail-content" v-else>
      <!-- 任务头部信息 -->
      <view class="task-header">
        <view class="header-top">
          <text class="task-title">{{ task.title }}</text>
          <view class="task-status" :class="`status-${task.status}`">
            {{ getStatusText(task.status) }}
          </view>
        </view>
        
        <view class="header-meta">
          <view class="priority-info">
            <text class="meta-label">优先级：</text>
            <view class="priority-tag" :class="`priority-${task.priority}`">
              {{ getPriorityText(task.priority) }}
            </view>
          </view>
          
          <view class="date-info" v-if="task.dueDate">
            <text class="meta-label">截止日期：</text>
            <text class="date-text" :class="{ 'overdue': isOverdue }">
              {{ formatDate(task.dueDate, 'YYYY-MM-DD') }}
            </text>
          </view>
        </view>
      </view>

      <!-- 任务描述 -->
      <view class="task-description" v-if="task.description">
        <text class="section-title">任务描述</text>
        <text class="description-text">{{ task.description }}</text>
      </view>

      <!-- 任务标签 -->
      <view class="task-tags" v-if="task.tags.length > 0">
        <text class="section-title">标签</text>
        <view class="tags-list">
          <view class="tag" v-for="tag in task.tags" :key="tag">
            <text class="tag-text">{{ tag }}</text>
          </view>
        </view>
      </view>

      <!-- 时间信息 -->
      <view class="time-info">
        <text class="section-title">时间信息</text>
        <view class="time-item">
          <text class="time-label">创建时间：</text>
          <text class="time-value">{{ formatDate(task.createdAt, 'YYYY-MM-DD HH:mm') }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">更新时间：</text>
          <text class="time-value">{{ formatDate(task.updatedAt, 'YYYY-MM-DD HH:mm') }}</text>
        </view>
        <view class="time-item">
          <text class="time-label">相对时间：</text>
          <text class="time-value">{{ getRelativeTime(task.createdAt) }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button 
          class="action-btn status-btn" 
          :class="`btn-${task.status}`"
          @click="handleStatusToggle"
        >
          {{ getNextStatusText() }}
        </button>
        
        <button class="action-btn edit-btn" @click="handleEdit">
          编辑任务
        </button>
        
        <button class="action-btn delete-btn" @click="handleDelete">
          删除任务
        </button>
      </view>
    </view>

    <!-- 编辑表单弹窗 -->
    <view class="form-modal" v-if="showEditForm" @click="hideEditForm">
      <view class="form-container" @click.stop>
        <TaskForm
          :task="task"
          mode="edit"
          :loading="formLoading"
          @submit="handleFormSubmit"
          @cancel="hideEditForm"
          @delete="handleFormDelete"
        />
      </view>
    </view>

    <!-- 全局加载遮罩 */
    <view class="loading-overlay" v-if="globalLoading">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 任务详情页面的逻辑部分
 * 处理任务详情的显示、编辑、删除等操作
 */

import { ref, computed, onMounted } from 'vue'
import type { Task, TaskStatus, UpdateTaskData } from '@/types'
import { taskService } from '@/services/taskService'
import { getStatusText, getPriorityText, formatDate, getRelativeTime } from '@/utils'
import TaskForm from '@/components/TaskForm.vue'

// 页面状态
const loading = ref(false)              // 页面加载状态
const globalLoading = ref(false)        // 全局加载状态
const formLoading = ref(false)          // 表单提交状态
const showEditForm = ref(false)         // 是否显示编辑表单

// 数据状态
const task = ref<Task | null>(null)     // 任务详情数据
const taskId = ref('')                  // 任务ID

/**
 * 计算属性：判断任务是否已过期
 */
const isOverdue = computed(() => {
  if (!task.value?.dueDate) return false
  return new Date() > task.value.dueDate && task.value.status !== 'completed'
})

/**
 * 获取下一个状态的文本
 */
const getNextStatusText = (): string => {
  if (!task.value) return ''
  
  switch (task.value.status) {
    case 'pending':
      return '开始任务'
    case 'in_progress':
      return '完成任务'
    case 'completed':
      return '重新开始'
    default:
      return '开始任务'
  }
}

/**
 * 加载任务详情
 */
const loadTaskDetail = async (): Promise<void> => {
  try {
    loading.value = true
    const taskData = await taskService.getTaskById(taskId.value)
    task.value = taskData
  } catch (error) {
    console.error('加载任务详情失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

/**
 * 处理状态切换
 */
const handleStatusToggle = async (): Promise<void> => {
  if (!task.value) return
  
  try {
    globalLoading.value = true
    const updatedTask = await taskService.toggleTaskStatus(task.value.id)
    task.value = updatedTask
    
    uni.showToast({
      title: '状态更新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('更新任务状态失败:', error)
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

/**
 * 处理编辑任务
 */
const handleEdit = (): void => {
  showEditForm.value = true
}

/**
 * 隐藏编辑表单
 */
const hideEditForm = (): void => {
  showEditForm.value = false
}

/**
 * 处理表单提交
 */
const handleFormSubmit = async (data: UpdateTaskData): Promise<void> => {
  if (!task.value) return
  
  try {
    formLoading.value = true
    const updatedTask = await taskService.updateTask(task.value.id, data)
    task.value = updatedTask
    
    uni.showToast({
      title: '更新成功',
      icon: 'success'
    })
    
    hideEditForm()
  } catch (error) {
    console.error('更新任务失败:', error)
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none'
    })
  } finally {
    formLoading.value = false
  }
}

/**
 * 处理删除任务
 */
const handleDelete = async (): Promise<void> => {
  if (!task.value) return
  
  try {
    // 显示确认对话框
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个任务吗？此操作不可恢复。',
        success: (res) => resolve(res.confirm),
        fail: () => resolve(false)
      })
    })
    
    if (!result) return
    
    globalLoading.value = true
    await taskService.deleteTask(task.value.id)
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
    
    // 返回上一页
    setTimeout(() => {
      goBack()
    }, 1000)
  } catch (error) {
    console.error('删除任务失败:', error)
    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

/**
 * 处理表单删除
 */
const handleFormDelete = async (taskId: string): Promise<void> => {
  hideEditForm()
  await handleDelete()
}

/**
 * 返回上一页
 */
const goBack = (): void => {
  uni.navigateBack({
    delta: 1
  })
}

/**
 * 页面初始化
 */
const initPage = (): void => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const options = currentPage.options || {}
  
  taskId.value = options.id || ''
  
  if (!taskId.value) {
    uni.showToast({
      title: '任务ID不存在',
      icon: 'none'
    })
    setTimeout(goBack, 1500)
    return
  }
  
  // 加载任务详情
  loadTaskDetail()
}

// 页面生命周期
onMounted(() => {
  initPage()
})

// 页面显示时刷新数据
const onShow = (): void => {
  if (taskId.value) {
    loadTaskDetail()
  }
}

// 下拉刷新
const onPullDownRefresh = async (): Promise<void> => {
  await loadTaskDetail()
  uni.stopPullDownRefresh()
}

// 导出页面方法供uni-app调用
defineExpose({
  onShow,
  onPullDownRefresh
})
</script>

<style lang="scss" scoped>
/**
 * 任务详情页面的样式
 */

.task-detail-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.loading-text {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
}

/* 任务不存在状态 */
.not-found-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  text-align: center;
}

.not-found-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.not-found-description {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.back-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }
}

/* 详情内容 */
.detail-content {
  max-width: 800px;
  margin: 0 auto;
}

/* 任务头部 */
.task-header {
  background-color: var(--bg-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.header-top {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--spacing-md);
}

.task-title {
  flex: 1;
  font-size: var(--font-size-xxl);
  font-weight: 600;
  color: var(--text-primary);
  line-height: var(--line-height-tight);
  margin-right: var(--spacing-md);
}

.task-status {
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-large);
  font-size: var(--font-size-sm);
  font-weight: 500;
  white-space: nowrap;

  &.status-pending {
    background-color: rgba(255, 149, 0, 0.1);
    color: #FF9500;
  }

  &.status-in_progress {
    background-color: rgba(0, 122, 255, 0.1);
    color: #007AFF;
  }

  &.status-completed {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34C759;
  }
}

.header-meta {
  display: flex;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.priority-info,
.date-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.meta-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.priority-tag {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-small);
  font-size: var(--font-size-xs);
  font-weight: 500;

  &.priority-low {
    background-color: rgba(52, 199, 89, 0.1);
    color: #34C759;
  }

  &.priority-medium {
    background-color: rgba(255, 149, 0, 0.1);
    color: #FF9500;
  }

  &.priority-high {
    background-color: rgba(255, 59, 48, 0.1);
    color: #FF3B30;
  }
}

.date-text {
  font-size: var(--font-size-sm);
  color: var(--text-primary);

  &.overdue {
    color: var(--error-color);
    font-weight: 600;
  }
}

/* 内容区块 */
.task-description,
.task-tags,
.time-info {
  background-color: var(--bg-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: block;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.description-text {
  font-size: var(--font-size-md);
  color: var(--text-secondary);
  line-height: var(--line-height-normal);
  white-space: pre-wrap;
}

/* 标签列表 */
.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.tag {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-color);
  color: var(--text-white);
  border-radius: var(--radius-large);
}

.tag-text {
  font-size: var(--font-size-sm);
}

/* 时间信息 */
.time-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--border-light);

  &:last-child {
    border-bottom: none;
  }
}

.time-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.time-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.action-btn {
  flex: 1;
  padding: var(--spacing-md);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.98);
  }
}

.status-btn {
  background-color: var(--primary-color);
  color: var(--text-white);

  &.btn-pending {
    background-color: #FF9500;
  }

  &.btn-in_progress {
    background-color: #007AFF;
  }

  &.btn-completed {
    background-color: #34C759;
  }
}

.edit-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.delete-btn {
  background-color: transparent;
  color: var(--error-color);
  border: 1px solid var(--error-color);
}

/* 表单弹窗 */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.form-container {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

/* 全局加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-detail-page {
    padding: var(--spacing-sm);
  }

  .task-header {
    padding: var(--spacing-md);
  }

  .header-top {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .task-title {
    font-size: var(--font-size-xl);
    margin-right: 0;
  }

  .header-meta {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .action-buttons {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .time-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .form-modal {
    padding: var(--spacing-sm);
  }
}
</style>

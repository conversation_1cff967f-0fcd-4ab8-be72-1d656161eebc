<!--
  任务管理主页面
  这是应用的主要页面，展示任务列表和相关功能
  包含任务的增删改查、筛选排序等功能
-->

<template>
  <view class="task-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">任务管理器</text>
      <button class="add-btn" @click="showCreateForm">
        + 新建任务
      </button>
    </view>

    <!-- 任务列表组件 -->
    <TaskList
      :tasks="tasks"
      :loading="loading"
      :stats="taskStats"
      :show-header="true"
      :show-search="true"
      :show-stats="true"
      :show-task-actions="true"
      :has-more="false"
      @task-select="handleTaskSelect"
      @filter-change="handleFilterChange"
      @sort-change="handleSortChange"
      @search-change="handleSearchChange"
      @status-change="handleStatusChange"
      @edit-task="handleEditTask"
      @delete-task="handleDeleteTask"
      @create-task="showCreateForm"
      @load-more="handleLoadMore"
    />

    <!-- 任务表单弹窗 -->
    <view class="form-modal" v-if="showForm" @click="hideForm">
      <view class="form-container" @click.stop>
        <TaskForm
          :task="currentTask"
          :mode="formMode"
          :loading="formLoading"
          @submit="handleFormSubmit"
          @cancel="hideForm"
          @delete="handleFormDelete"
        />
      </view>
    </view>

    <!-- 加载提示 -->
    <view class="loading-overlay" v-if="globalLoading">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 任务管理主页面的逻辑部分
 * 使用Vue3的Composition API和TypeScript
 * 管理整个页面的状态和数据流
 */

import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import type {
  Task,
  TaskStatus,
  TaskFilter,
  SortOption,
  CreateTaskData,
  UpdateTaskData,
  TaskStats
} from '@/types'
import { taskService } from '@/services/taskService'
import { debounce } from '@/utils'
import TaskList from '@/components/TaskList.vue'
import TaskForm from '@/components/TaskForm.vue'

// 页面状态管理
const loading = ref(false)              // 列表加载状态
const globalLoading = ref(false)        // 全局加载状态
const formLoading = ref(false)          // 表单提交状态
const showForm = ref(false)             // 是否显示表单
const formMode = ref<'create' | 'edit'>('create')  // 表单模式

// 数据状态
const tasks = ref<Task[]>([])           // 任务列表
const taskStats = ref<TaskStats>({      // 任务统计
  total: 0,
  pending: 0,
  inProgress: 0,
  completed: 0
})
const currentTask = ref<Task | undefined>()  // 当前编辑的任务

// 筛选和排序状态
const currentFilter = reactive<TaskFilter>({})
const currentSort = reactive<SortOption>({
  field: 'createdAt',
  order: 'desc'
})
const searchText = ref('')

/**
 * 加载任务列表
 */
const loadTasks = async (): Promise<void> => {
  try {
    loading.value = true
    const [tasksData, statsData] = await Promise.all([
      taskService.getAllTasks(),
      taskService.getTaskStats()
    ])

    tasks.value = tasksData
    taskStats.value = statsData
  } catch (error) {
    console.error('加载任务列表失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

/**
 * 刷新统计信息
 */
const refreshStats = async (): Promise<void> => {
  try {
    const stats = await taskService.getTaskStats()
    taskStats.value = stats
  } catch (error) {
    console.error('刷新统计信息失败:', error)
  }
}

/**
 * 显示创建表单
 */
const showCreateForm = (): void => {
  currentTask.value = undefined
  formMode.value = 'create'
  showForm.value = true
}

/**
 * 显示编辑表单
 */
const showEditForm = (task: Task): void => {
  currentTask.value = task
  formMode.value = 'edit'
  showForm.value = true
}

/**
 * 隐藏表单
 */
const hideForm = (): void => {
  showForm.value = false
  currentTask.value = undefined
}

/**
 * 处理任务选择（点击任务卡片）
 */
const handleTaskSelect = (task: Task): void => {
  // 导航到任务详情页面
  uni.navigateTo({
    url: `/pages/task-detail/task-detail?id=${task.id}`
  })
}

/**
 * 处理筛选条件改变
 */
const handleFilterChange = (filter: TaskFilter): void => {
  Object.assign(currentFilter, filter)
  console.log('筛选条件改变:', filter)
}

/**
 * 处理排序选项改变
 */
const handleSortChange = (sortOption: SortOption): void => {
  Object.assign(currentSort, sortOption)
  console.log('排序选项改变:', sortOption)
}

/**
 * 处理搜索文本改变
 */
const handleSearchChange = debounce((text: string): void => {
  searchText.value = text
  console.log('搜索文本改变:', text)
}, 300)

/**
 * 处理任务状态改变
 */
const handleStatusChange = async (taskId: string, status: TaskStatus): Promise<void> => {
  try {
    globalLoading.value = true
    await taskService.updateTask(taskId, { status })

    // 更新本地数据
    const taskIndex = tasks.value.findIndex(t => t.id === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].status = status
      tasks.value[taskIndex].updatedAt = new Date()
    }

    // 刷新统计信息
    await refreshStats()

    uni.showToast({
      title: '状态更新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('更新任务状态失败:', error)
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

/**
 * 处理编辑任务
 */
const handleEditTask = (task: Task): void => {
  showEditForm(task)
}

/**
 * 处理删除任务
 */
const handleDeleteTask = async (taskId: string): Promise<void> => {
  try {
    // 显示确认对话框
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个任务吗？此操作不可恢复。',
        success: (res) => resolve(res.confirm),
        fail: () => resolve(false)
      })
    })

    if (!result) return

    globalLoading.value = true
    await taskService.deleteTask(taskId)

    // 从本地数据中移除
    const taskIndex = tasks.value.findIndex(t => t.id === taskId)
    if (taskIndex !== -1) {
      tasks.value.splice(taskIndex, 1)
    }

    // 刷新统计信息
    await refreshStats()

    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('删除任务失败:', error)
    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

/**
 * 处理表单提交
 */
const handleFormSubmit = async (data: CreateTaskData | UpdateTaskData): Promise<void> => {
  try {
    formLoading.value = true

    if (formMode.value === 'create') {
      // 创建新任务
      const newTask = await taskService.createTask(data as CreateTaskData)
      tasks.value.unshift(newTask)

      uni.showToast({
        title: '创建成功',
        icon: 'success'
      })
    } else {
      // 更新现有任务
      if (!currentTask.value) return

      const updatedTask = await taskService.updateTask(currentTask.value.id, data as UpdateTaskData)

      // 更新本地数据
      const taskIndex = tasks.value.findIndex(t => t.id === currentTask.value!.id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = updatedTask
      }

      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
    }

    // 刷新统计信息
    await refreshStats()

    // 隐藏表单
    hideForm()
  } catch (error) {
    console.error('保存任务失败:', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    })
  } finally {
    formLoading.value = false
  }
}

/**
 * 处理表单删除
 */
const handleFormDelete = async (taskId: string): Promise<void> => {
  hideForm()
  await handleDeleteTask(taskId)
}

/**
 * 处理加载更多
 */
const handleLoadMore = (): void => {
  console.log('加载更多')
}

/**
 * 初始化页面数据
 */
const initPageData = async (): Promise<void> => {
  try {
    globalLoading.value = true

    // 初始化示例数据（仅在首次使用时）
    await taskService.initSampleData()

    // 加载任务列表
    await loadTasks()
  } catch (error) {
    console.error('初始化页面数据失败:', error)
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

// 页面生命周期
onMounted(() => {
  initPageData()
})

// 页面卸载时的清理工作
onUnmounted(() => {
  // 清理定时器、事件监听器等
})
</script>

<style lang="scss" scoped>
/**
 * 任务管理主页面的样式
 */

.task-page {
  min-height: 100vh;
  background-color: var(--bg-secondary);
  padding: var(--spacing-md);
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-md);
  background-color: var(--bg-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
  margin-bottom: var(--spacing-md);
}

.page-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--text-primary);
}

.add-btn {
  padding: var(--spacing-sm) var(--spacing-md);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.98);
  }
}

/* 表单弹窗 */
.form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--spacing-md);
}

.form-container {
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

/* 加载遮罩 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
}

.loading-text {
  color: var(--text-white);
  font-size: var(--font-size-lg);
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-page {
    padding: var(--spacing-sm);
  }

  .page-header {
    padding: var(--spacing-md) var(--spacing-sm);
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }

  .page-title {
    font-size: var(--font-size-lg);
  }

  .add-btn {
    width: 100%;
  }

  .form-modal {
    padding: var(--spacing-sm);
  }
}
</style>

<!--
  简化版任务卡片组件
  用于显示单个任务的信息，包括标题、描述、状态、优先级等
  支持点击、状态切换、删除等操作
-->

<template>
  <view class="task-card" @click="handleCardClick">
    <!-- 任务主要信息区域 -->
    <view class="task-header">
      <!-- 任务标题和状态 -->
      <view class="task-title-row">
        <text class="task-title" :class="{ 'completed': task.status === 'completed' }">
          {{ task.title }}
        </text>
        <view class="task-status" :class="`status-${task.status}`">
          {{ getStatusText(task.status) }}
        </view>
      </view>
      
      <!-- 任务描述 -->
      <text class="task-description" v-if="task.description">
        {{ task.description }}
      </text>
    </view>

    <!-- 任务元信息区域 -->
    <view class="task-meta">
      <!-- 优先级标签 -->
      <view class="priority-tag" :class="`priority-${task.priority}`">
        <text class="priority-text">{{ getPriorityText(task.priority) }}</text>
      </view>

      <!-- 截止日期 -->
      <view class="due-date" v-if="task.dueDate">
        <text class="due-date-text" :class="{ 'overdue': isOverdue }">
          {{ formatDate(task.dueDate, 'MM-DD') }}
        </text>
      </view>

      <!-- 创建时间 -->
      <text class="created-time">
        {{ getRelativeTime(task.createdAt) }}
      </text>
    </view>

    <!-- 标签区域 -->
    <view class="task-tags" v-if="task.tags.length > 0">
      <view class="tag" v-for="tag in task.tags" :key="tag">
        <text class="tag-text">{{ tag }}</text>
      </view>
    </view>

    <!-- 操作按钮区域 -->
    <view class="task-actions" v-if="showActions">
      <!-- 状态切换按钮 -->
      <button 
        class="action-btn status-btn" 
        :class="`btn-${task.status}`"
        @click.stop="handleStatusToggle"
      >
        {{ getNextStatusText() }}
      </button>

      <!-- 编辑按钮 -->
      <button class="action-btn edit-btn" @click.stop="handleEdit">
        编辑
      </button>

      <!-- 删除按钮 -->
      <button class="action-btn delete-btn" @click.stop="handleDelete">
        删除
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
// 任务卡片组件的逻辑部分
// 使用Vue3的Composition API和TypeScript

import { computed } from 'vue'
import type { Task, TaskStatus } from '@/types'
import { getStatusText, getPriorityText, formatDate, getRelativeTime } from '@/utils'

// 定义组件接收的属性
interface Props {
  task: Task                    // 任务数据
  showActions?: boolean         // 是否显示操作按钮
  compact?: boolean            // 是否使用紧凑模式
}

// 定义组件发出的事件
interface Emits {
  (e: 'task-click', task: Task): void                           // 任务卡片点击事件
  (e: 'status-change', taskId: string, status: TaskStatus): void // 状态改变事件
  (e: 'edit-task', task: Task): void                           // 编辑任务事件
  (e: 'delete-task', taskId: string): void                     // 删除任务事件
}

// 接收属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})

// 定义事件发射器
const emit = defineEmits<Emits>()

// 计算属性：判断任务是否已过期
const isOverdue = computed(() => {
  if (!props.task.dueDate) return false
  return new Date() > props.task.dueDate && props.task.status !== 'completed'
})

// 获取下一个状态的文本
const getNextStatusText = (): string => {
  switch (props.task.status) {
    case 'pending':
      return '开始'
    case 'in_progress':
      return '完成'
    case 'completed':
      return '重新开始'
    default:
      return '开始'
  }
}

// 处理卡片点击事件
const handleCardClick = (): void => {
  emit('task-click', props.task)
}

// 处理状态切换事件
const handleStatusToggle = (): void => {
  let nextStatus: TaskStatus
  
  switch (props.task.status) {
    case 'pending':
      nextStatus = 'in_progress'
      break
    case 'in_progress':
      nextStatus = 'completed'
      break
    case 'completed':
      nextStatus = 'pending'
      break
    default:
      nextStatus = 'pending'
  }
  
  emit('status-change', props.task.id, nextStatus)
}

// 处理编辑事件
const handleEdit = (): void => {
  emit('edit-task', props.task)
}

// 处理删除事件
const handleDelete = (): void => {
  emit('delete-task', props.task.id)
}
</script>

<style scoped>
/* 任务卡片组件的样式 */

.task-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.task-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 任务头部区域 */
.task-header {
  margin-bottom: 8px;
}

.task-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 4px;
}

.task-title {
  flex: 1;
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  line-height: 1.2;
  margin-right: 8px;
}

.task-title.completed {
  text-decoration: line-through;
  color: #8E8E93;
}

.task-description {
  font-size: 14px;
  color: #8E8E93;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 任务状态标签 */
.task-status {
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.task-status.status-pending {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.task-status.status-in_progress {
  background-color: rgba(0, 122, 255, 0.1);
  color: #007AFF;
}

.task-status.status-completed {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

/* 任务元信息区域 */
.task-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.priority-tag {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}

.priority-tag.priority-low {
  background-color: rgba(52, 199, 89, 0.1);
  color: #34C759;
}

.priority-tag.priority-medium {
  background-color: rgba(255, 149, 0, 0.1);
  color: #FF9500;
}

.priority-tag.priority-high {
  background-color: rgba(255, 59, 48, 0.1);
  color: #FF3B30;
}

.due-date-text {
  font-size: 12px;
  color: #8E8E93;
}

.due-date-text.overdue {
  color: #FF3B30;
  font-weight: 600;
}

.created-time {
  font-size: 12px;
  color: #C7C7CC;
  margin-left: auto;
}

/* 标签区域 */
.task-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.tag {
  padding: 2px 6px;
  background-color: #F2F2F7;
  border-radius: 12px;
}

.tag-text {
  font-size: 10px;
  color: #8E8E93;
}

/* 操作按钮区域 */
.task-actions {
  display: flex;
  gap: 8px;
  padding-top: 8px;
  border-top: 1px solid #E5E5EA;
}

.action-btn {
  flex: 1;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  opacity: 0.8;
}

.status-btn {
  background-color: #007AFF;
  color: white;
}

.status-btn.btn-pending {
  background-color: #FF9500;
}

.status-btn.btn-in_progress {
  background-color: #007AFF;
}

.status-btn.btn-completed {
  background-color: #34C759;
}

.edit-btn {
  background-color: transparent;
  color: #007AFF;
  border: 1px solid #007AFF;
}

.delete-btn {
  background-color: transparent;
  color: #FF3B30;
  border: 1px solid #FF3B30;
}
</style>

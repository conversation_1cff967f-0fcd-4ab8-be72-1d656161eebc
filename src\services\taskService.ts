/**
 * 任务数据服务
 * 负责任务数据的增删改查操作
 * 在实际项目中，这里会调用后端API，现在使用本地存储模拟
 */

import { Task, TaskStatus, TaskPriority, CreateTaskData, UpdateTaskData, TaskStats } from '@/types'
import { generateId, storage } from '@/utils'

// 本地存储的键名
const STORAGE_KEY = 'tasks'

/**
 * 任务服务类
 * 提供任务数据的CRUD操作
 */
class TaskService {
  /**
   * 获取所有任务
   * @returns {Promise<Task[]>} 任务列表
   */
  async getAllTasks(): Promise<Task[]> {
    try {
      // 模拟网络延迟
      await this.delay(300)
      
      const tasks = storage.get<Task[]>(STORAGE_KEY, [])
      
      // 将字符串日期转换为Date对象
      return tasks.map(task => ({
        ...task,
        createdAt: new Date(task.createdAt),
        updatedAt: new Date(task.updatedAt),
        dueDate: task.dueDate ? new Date(task.dueDate) : undefined
      }))
    } catch (error) {
      console.error('获取任务列表失败:', error)
      throw new Error('获取任务列表失败')
    }
  }

  /**
   * 根据ID获取单个任务
   * @param {string} id - 任务ID
   * @returns {Promise<Task | null>} 任务对象或null
   */
  async getTaskById(id: string): Promise<Task | null> {
    try {
      await this.delay(200)
      
      const tasks = await this.getAllTasks()
      const task = tasks.find(t => t.id === id)
      
      return task || null
    } catch (error) {
      console.error('获取任务详情失败:', error)
      throw new Error('获取任务详情失败')
    }
  }

  /**
   * 创建新任务
   * @param {CreateTaskData} taskData - 任务数据
   * @returns {Promise<Task>} 创建的任务
   */
  async createTask(taskData: CreateTaskData): Promise<Task> {
    try {
      await this.delay(500)
      
      const now = new Date()
      const newTask: Task = {
        id: generateId(),
        title: taskData.title,
        description: taskData.description,
        status: TaskStatus.PENDING,
        priority: taskData.priority,
        createdAt: now,
        updatedAt: now,
        dueDate: taskData.dueDate,
        tags: taskData.tags
      }

      const tasks = await this.getAllTasks()
      tasks.push(newTask)
      
      storage.set(STORAGE_KEY, tasks)
      
      return newTask
    } catch (error) {
      console.error('创建任务失败:', error)
      throw new Error('创建任务失败')
    }
  }

  /**
   * 更新任务
   * @param {string} id - 任务ID
   * @param {UpdateTaskData} updateData - 更新数据
   * @returns {Promise<Task>} 更新后的任务
   */
  async updateTask(id: string, updateData: UpdateTaskData): Promise<Task> {
    try {
      await this.delay(400)
      
      const tasks = await this.getAllTasks()
      const taskIndex = tasks.findIndex(t => t.id === id)
      
      if (taskIndex === -1) {
        throw new Error('任务不存在')
      }

      const updatedTask: Task = {
        ...tasks[taskIndex],
        ...updateData,
        updatedAt: new Date()
      }

      tasks[taskIndex] = updatedTask
      storage.set(STORAGE_KEY, tasks)
      
      return updatedTask
    } catch (error) {
      console.error('更新任务失败:', error)
      throw new Error('更新任务失败')
    }
  }

  /**
   * 删除任务
   * @param {string} id - 任务ID
   * @returns {Promise<boolean>} 是否删除成功
   */
  async deleteTask(id: string): Promise<boolean> {
    try {
      await this.delay(300)
      
      const tasks = await this.getAllTasks()
      const filteredTasks = tasks.filter(t => t.id !== id)
      
      if (filteredTasks.length === tasks.length) {
        throw new Error('任务不存在')
      }

      storage.set(STORAGE_KEY, filteredTasks)
      
      return true
    } catch (error) {
      console.error('删除任务失败:', error)
      throw new Error('删除任务失败')
    }
  }

  /**
   * 切换任务状态
   * @param {string} id - 任务ID
   * @returns {Promise<Task>} 更新后的任务
   */
  async toggleTaskStatus(id: string): Promise<Task> {
    try {
      const task = await this.getTaskById(id)
      if (!task) {
        throw new Error('任务不存在')
      }

      let newStatus: TaskStatus
      switch (task.status) {
        case TaskStatus.PENDING:
          newStatus = TaskStatus.IN_PROGRESS
          break
        case TaskStatus.IN_PROGRESS:
          newStatus = TaskStatus.COMPLETED
          break
        case TaskStatus.COMPLETED:
          newStatus = TaskStatus.PENDING
          break
        default:
          newStatus = TaskStatus.PENDING
      }

      return await this.updateTask(id, { status: newStatus })
    } catch (error) {
      console.error('切换任务状态失败:', error)
      throw new Error('切换任务状态失败')
    }
  }

  /**
   * 获取任务统计信息
   * @returns {Promise<TaskStats>} 统计信息
   */
  async getTaskStats(): Promise<TaskStats> {
    try {
      await this.delay(200)
      
      const tasks = await this.getAllTasks()
      
      const stats: TaskStats = {
        total: tasks.length,
        pending: tasks.filter(t => t.status === TaskStatus.PENDING).length,
        inProgress: tasks.filter(t => t.status === TaskStatus.IN_PROGRESS).length,
        completed: tasks.filter(t => t.status === TaskStatus.COMPLETED).length
      }

      return stats
    } catch (error) {
      console.error('获取统计信息失败:', error)
      throw new Error('获取统计信息失败')
    }
  }

  /**
   * 获取所有标签
   * @returns {Promise<string[]>} 标签列表
   */
  async getAllTags(): Promise<string[]> {
    try {
      await this.delay(100)
      
      const tasks = await this.getAllTasks()
      const allTags = tasks.flatMap(task => task.tags)
      
      // 去重并排序
      return [...new Set(allTags)].sort()
    } catch (error) {
      console.error('获取标签列表失败:', error)
      throw new Error('获取标签列表失败')
    }
  }

  /**
   * 批量删除任务
   * @param {string[]} ids - 任务ID数组
   * @returns {Promise<boolean>} 是否删除成功
   */
  async batchDeleteTasks(ids: string[]): Promise<boolean> {
    try {
      await this.delay(500)
      
      const tasks = await this.getAllTasks()
      const filteredTasks = tasks.filter(t => !ids.includes(t.id))
      
      storage.set(STORAGE_KEY, filteredTasks)
      
      return true
    } catch (error) {
      console.error('批量删除任务失败:', error)
      throw new Error('批量删除任务失败')
    }
  }

  /**
   * 初始化示例数据
   * 用于演示，创建一些示例任务
   * @returns {Promise<void>}
   */
  async initSampleData(): Promise<void> {
    try {
      const existingTasks = await this.getAllTasks()
      if (existingTasks.length > 0) {
        return // 如果已有数据，不重复初始化
      }

      const sampleTasks: CreateTaskData[] = [
        {
          title: '学习Vue3组合式API',
          description: '深入学习Vue3的Composition API，包括ref、reactive、computed等核心概念',
          priority: TaskPriority.HIGH,
          tags: ['学习', 'Vue3', '前端'],
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7天后
        },
        {
          title: '完成项目文档',
          description: '编写项目的技术文档和用户手册',
          priority: TaskPriority.MEDIUM,
          tags: ['文档', '工作'],
          dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3天后
        },
        {
          title: '代码重构',
          description: '重构旧项目的代码，提高代码质量和可维护性',
          priority: TaskPriority.LOW,
          tags: ['重构', '代码优化'],
          dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000) // 14天后
        }
      ]

      for (const taskData of sampleTasks) {
        await this.createTask(taskData)
      }

      console.log('示例数据初始化完成')
    } catch (error) {
      console.error('初始化示例数据失败:', error)
    }
  }

  /**
   * 清空所有任务
   * @returns {Promise<boolean>} 是否清空成功
   */
  async clearAllTasks(): Promise<boolean> {
    try {
      await this.delay(200)
      storage.remove(STORAGE_KEY)
      return true
    } catch (error) {
      console.error('清空任务失败:', error)
      throw new Error('清空任务失败')
    }
  }

  /**
   * 模拟网络延迟
   * @param {number} ms - 延迟毫秒数
   * @returns {Promise<void>}
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出单例实例
export const taskService = new TaskService()

// 默认导出
export default taskService

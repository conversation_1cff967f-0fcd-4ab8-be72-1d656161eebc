# UniApp 任务管理器 Demo

这是一个使用 Vue3 + TypeScript + UniApp 开发的任务管理小程序示例项目。项目采用完全组件化的架构，包含详细的注释，适合学习和参考。

## 🚀 项目特点

- **Vue3 Composition API**: 使用最新的 Vue3 语法和 Composition API
- **TypeScript**: 完整的 TypeScript 类型定义，提供更好的开发体验
- **组件化架构**: 所有功能都拆分为独立的组件，便于维护和复用
- **文件分离**: 类型、工具函数、样式、服务等都独立成文件
- **详细注释**: 每个文件、函数、组件都有详细的中文注释
- **响应式设计**: 支持不同屏幕尺寸的设备
- **现代化样式**: 使用 SCSS 和 CSS 变量，支持深色模式

## 📁 项目结构

```
src/
├── components/          # 组件目录
│   ├── TaskCard.vue    # 任务卡片组件
│   ├── TaskList.vue    # 任务列表组件
│   └── TaskForm.vue    # 任务表单组件
├── pages/              # 页面目录
│   ├── index/          # 主页面
│   │   └── index.vue
│   └── task-detail/    # 任务详情页面
│       └── task-detail.vue
├── services/           # 服务层
│   └── taskService.ts  # 任务数据服务
├── styles/             # 样式文件
│   └── common.scss     # 通用样式和变量
├── types/              # 类型定义
│   └── index.ts        # 所有类型定义
├── utils/              # 工具函数
│   └── index.ts        # 通用工具函数
├── App.vue             # 应用主组件
├── main.ts             # 应用入口文件
├── pages.json          # 页面配置文件
└── manifest.json       # 应用配置文件
```

## 🛠️ 技术栈

- **框架**: UniApp + Vue3
- **语言**: TypeScript
- **样式**: SCSS + CSS Variables
- **状态管理**: Vue3 Composition API
- **数据存储**: 本地存储 (localStorage)
- **构建工具**: Vite

## 📱 功能特性

### 任务管理
- ✅ 创建任务
- ✅ 编辑任务
- ✅ 删除任务
- ✅ 任务状态切换（待完成 → 进行中 → 已完成）
- ✅ 任务优先级设置（低、中、高）
- ✅ 任务截止日期
- ✅ 任务标签管理

### 列表功能
- ✅ 任务列表展示
- ✅ 任务搜索
- ✅ 任务筛选（按状态、优先级）
- ✅ 任务排序（按创建时间、更新时间、优先级等）
- ✅ 任务统计信息

### 用户体验
- ✅ 响应式设计
- ✅ 加载状态提示
- ✅ 错误处理
- ✅ 操作反馈
- ✅ 确认对话框

## 🎯 核心组件说明

### TaskCard 组件
任务卡片组件，用于显示单个任务的信息。

**主要功能**:
- 显示任务标题、描述、状态、优先级
- 支持任务状态快速切换
- 提供编辑和删除操作
- 显示任务标签和时间信息

**使用示例**:
```vue
<TaskCard
  :task="task"
  :show-actions="true"
  @task-click="handleTaskClick"
  @status-change="handleStatusChange"
  @edit-task="handleEditTask"
  @delete-task="handleDeleteTask"
/>
```

### TaskList 组件
任务列表组件，用于展示任务列表和相关功能。

**主要功能**:
- 任务列表展示
- 搜索和筛选功能
- 排序功能
- 统计信息显示
- 空状态处理

**使用示例**:
```vue
<TaskList
  :tasks="tasks"
  :loading="loading"
  :stats="taskStats"
  @task-select="handleTaskSelect"
  @filter-change="handleFilterChange"
  @sort-change="handleSortChange"
/>
```

### TaskForm 组件
任务表单组件，用于创建和编辑任务。

**主要功能**:
- 任务信息输入
- 表单验证
- 支持创建和编辑模式
- 标签管理
- 日期选择

**使用示例**:
```vue
<TaskForm
  :task="currentTask"
  :mode="formMode"
  @submit="handleFormSubmit"
  @cancel="hideForm"
  @delete="handleFormDelete"
/>
```

## 🔧 开发指南

### 环境要求
- Node.js >= 16
- npm 或 yarn
- HBuilderX 或 VS Code

### 安装依赖
```bash
npm install
```

### 开发运行
```bash
# H5 开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# 支付宝小程序开发
npm run dev:mp-alipay
```

### 构建发布
```bash
# H5 构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# 支付宝小程序构建
npm run build:mp-alipay
```

## 📚 学习要点

### 1. Vue3 Composition API
项目中大量使用了 Vue3 的 Composition API，包括：
- `ref` 和 `reactive` 用于响应式数据
- `computed` 用于计算属性
- `watch` 用于监听数据变化
- `onMounted` 等生命周期钩子

### 2. TypeScript 类型系统
- 完整的类型定义在 `src/types/index.ts`
- 组件 Props 和 Emits 的类型定义
- 服务层的类型约束
- 工具函数的类型安全

### 3. 组件通信
- Props 向下传递数据
- Emits 向上传递事件
- 组件间的数据流管理

### 4. 状态管理
- 使用 Composition API 进行状态管理
- 数据的响应式更新
- 本地存储的使用

### 5. 样式架构
- CSS 变量的使用
- SCSS 的嵌套和混入
- 响应式设计的实现
- 主题切换的支持

## 🎨 样式系统

项目使用了完整的设计系统，包括：

### 颜色系统
- 主色调：蓝色系
- 功能色：成功、警告、错误、信息
- 中性色：文本、背景、边框

### 间距系统
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px
- xxl: 48px

### 字体系统
- xs: 12px
- sm: 14px
- md: 16px
- lg: 18px
- xl: 20px
- xxl: 24px

## 🔍 代码规范

### 命名规范
- 组件名：PascalCase (如 TaskCard)
- 文件名：kebab-case (如 task-card.vue)
- 变量名：camelCase (如 taskList)
- 常量名：UPPER_SNAKE_CASE (如 STORAGE_KEY)

### 注释规范
- 每个文件都有文件头注释
- 每个函数都有功能说明
- 复杂逻辑都有行内注释
- 组件都有使用说明

### 代码组织
- 按功能模块组织文件
- 相关文件放在同一目录
- 公共代码提取到独立文件

## 🚀 扩展建议

如果你想基于这个项目继续开发，可以考虑以下扩展：

1. **用户系统**: 添加用户注册、登录功能
2. **数据同步**: 集成后端 API，实现数据云端同步
3. **团队协作**: 支持多人协作，任务分配
4. **通知提醒**: 添加任务提醒功能
5. **数据统计**: 更丰富的数据统计和图表
6. **主题定制**: 更多主题选项和个性化设置
7. **离线支持**: PWA 支持，离线使用
8. **性能优化**: 虚拟滚动、懒加载等优化

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

希望这个项目能帮助你学习 Vue3 + TypeScript + UniApp 的开发！如果有任何问题，请随时提出。

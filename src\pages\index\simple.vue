<!--
  简化版任务管理主页面
  这是应用的主要页面，展示任务列表和相关功能
  使用简单的CSS，避免复杂的SCSS语法
-->

<template>
  <view class="task-page">
    <!-- 页面头部 -->
    <view class="page-header">
      <text class="page-title">任务管理器</text>
      <button class="add-btn" @click="showCreateForm">
        + 新建任务
      </button>
    </view>

    <!-- 任务统计 -->
    <view class="stats-section">
      <view class="stat-item">
        <text class="stat-number">{{ taskStats.total }}</text>
        <text class="stat-label">总计</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ taskStats.pending }}</text>
        <text class="stat-label">待完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ taskStats.inProgress }}</text>
        <text class="stat-label">进行中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ taskStats.completed }}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>

    <!-- 任务列表 -->
    <view class="task-list">
      <!-- 加载状态 -->
      <view class="loading-section" v-if="loading">
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 空状态 -->
      <view class="empty-state" v-else-if="tasks.length === 0">
        <text class="empty-title">暂无任务</text>
        <text class="empty-description">还没有创建任何任务</text>
        <button class="create-btn" @click="showCreateForm">
          创建第一个任务
        </button>
      </view>

      <!-- 任务卡片列表 -->
      <view class="task-items" v-else>
        <TaskCardSimple
          v-for="task in tasks"
          :key="task.id"
          :task="task"
          :show-actions="true"
          @task-click="handleTaskClick"
          @status-change="handleStatusChange"
          @edit-task="handleEditTask"
          @delete-task="handleDeleteTask"
        />
      </view>
    </view>

    <!-- 任务表单弹窗 -->
    <view class="form-modal" v-if="showForm" @click="hideForm">
      <view class="form-container" @click.stop>
        <view class="form-header">
          <text class="form-title">
            {{ formMode === 'create' ? '创建任务' : '编辑任务' }}
          </text>
          <button class="close-btn" @click="hideForm">✕</button>
        </view>
        
        <view class="form-content">
          <!-- 任务标题 -->
          <view class="form-group">
            <label class="form-label">任务标题 *</label>
            <input
              class="form-input"
              type="text"
              placeholder="请输入任务标题"
              v-model="formData.title"
            />
          </view>

          <!-- 任务描述 -->
          <view class="form-group">
            <label class="form-label">任务描述</label>
            <textarea
              class="form-textarea"
              placeholder="请输入任务描述"
              v-model="formData.description"
            />
          </view>

          <!-- 优先级选择 -->
          <view class="form-group">
            <label class="form-label">优先级 *</label>
            <picker
              :value="priorityIndex"
              :range="priorityOptions"
              range-key="label"
              @change="handlePriorityChange"
            >
              <view class="picker-input">
                <text class="picker-text">{{ currentPriorityText }}</text>
                <text class="picker-arrow">▼</text>
              </view>
            </picker>
          </view>

          <!-- 表单操作按钮 -->
          <view class="form-actions">
            <button class="btn btn-cancel" @click="hideForm">
              取消
            </button>
            <button 
              class="btn btn-submit" 
              :disabled="!isFormValid || formLoading"
              @click="handleFormSubmit"
            >
              {{ formLoading ? '保存中...' : (formMode === 'create' ? '创建任务' : '保存修改') }}
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 加载提示 -->
    <view class="loading-overlay" v-if="globalLoading">
      <text class="loading-text">处理中...</text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 任务管理主页面的逻辑部分
// 使用Vue3的Composition API和TypeScript
// 管理整个页面的状态和数据流

import { ref, reactive, computed, onMounted } from 'vue'
import type { 
  Task, 
  TaskStatus, 
  TaskPriority,
  CreateTaskData, 
  UpdateTaskData,
  TaskStats 
} from '@/types'
import { taskService } from '@/services/taskService'
import TaskCardSimple from '@/components/TaskCardSimple.vue'

// 页面状态管理
const loading = ref(false)              // 列表加载状态
const globalLoading = ref(false)        // 全局加载状态
const formLoading = ref(false)          // 表单提交状态
const showForm = ref(false)             // 是否显示表单
const formMode = ref<'create' | 'edit'>('create')  // 表单模式

// 数据状态
const tasks = ref<Task[]>([])           // 任务列表
const taskStats = ref<TaskStats>({      // 任务统计
  total: 0,
  pending: 0,
  inProgress: 0,
  completed: 0
})
const currentTask = ref<Task | undefined>()  // 当前编辑的任务

// 表单数据
const formData = reactive({
  title: '',
  description: '',
  priority: 'medium' as TaskPriority,
  tags: [] as string[]
})

// 优先级选项
const priorityOptions = [
  { value: 'low', label: '低优先级' },
  { value: 'medium', label: '中优先级' },
  { value: 'high', label: '高优先级' }
]

// 计算属性：当前优先级索引
const priorityIndex = computed(() => {
  return priorityOptions.findIndex(option => option.value === formData.priority)
})

// 计算属性：当前优先级文本
const currentPriorityText = computed(() => {
  const option = priorityOptions[priorityIndex.value]
  return option ? option.label : '请选择优先级'
})

// 计算属性：表单是否有效
const isFormValid = computed(() => {
  return formData.title.trim() !== '' && formData.priority !== ''
})

// 加载任务列表
const loadTasks = async (): Promise<void> => {
  try {
    loading.value = true
    const [tasksData, statsData] = await Promise.all([
      taskService.getAllTasks(),
      taskService.getTaskStats()
    ])
    
    tasks.value = tasksData
    taskStats.value = statsData
  } catch (error) {
    console.error('加载任务列表失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 刷新统计信息
const refreshStats = async (): Promise<void> => {
  try {
    const stats = await taskService.getTaskStats()
    taskStats.value = stats
  } catch (error) {
    console.error('刷新统计信息失败:', error)
  }
}

// 显示创建表单
const showCreateForm = (): void => {
  currentTask.value = undefined
  formMode.value = 'create'
  formData.title = ''
  formData.description = ''
  formData.priority = 'medium'
  formData.tags = []
  showForm.value = true
}

// 隐藏表单
const hideForm = (): void => {
  showForm.value = false
  currentTask.value = undefined
}

// 处理任务点击
const handleTaskClick = (task: Task): void => {
  uni.navigateTo({
    url: `/pages/task-detail/task-detail?id=${task.id}`
  })
}

// 处理任务状态改变
const handleStatusChange = async (taskId: string, status: TaskStatus): Promise<void> => {
  try {
    globalLoading.value = true
    await taskService.updateTask(taskId, { status })
    
    // 更新本地数据
    const taskIndex = tasks.value.findIndex(t => t.id === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].status = status
      tasks.value[taskIndex].updatedAt = new Date()
    }
    
    // 刷新统计信息
    await refreshStats()
    
    uni.showToast({
      title: '状态更新成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('更新任务状态失败:', error)
    uni.showToast({
      title: '更新失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

// 处理编辑任务
const handleEditTask = (task: Task): void => {
  currentTask.value = task
  formMode.value = 'edit'
  formData.title = task.title
  formData.description = task.description
  formData.priority = task.priority
  formData.tags = [...task.tags]
  showForm.value = true
}

// 处理删除任务
const handleDeleteTask = async (taskId: string): Promise<void> => {
  try {
    const result = await new Promise<boolean>((resolve) => {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个任务吗？此操作不可恢复。',
        success: (res) => resolve(res.confirm),
        fail: () => resolve(false)
      })
    })
    
    if (!result) return
    
    globalLoading.value = true
    await taskService.deleteTask(taskId)
    
    // 从本地数据中移除
    const taskIndex = tasks.value.findIndex(t => t.id === taskId)
    if (taskIndex !== -1) {
      tasks.value.splice(taskIndex, 1)
    }
    
    // 刷新统计信息
    await refreshStats()
    
    uni.showToast({
      title: '删除成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('删除任务失败:', error)
    uni.showToast({
      title: '删除失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

// 处理优先级改变
const handlePriorityChange = (event: any): void => {
  const index = event.detail.value
  const selectedOption = priorityOptions[index]
  formData.priority = selectedOption.value as TaskPriority
}

// 处理表单提交
const handleFormSubmit = async (): Promise<void> => {
  if (!isFormValid.value) {
    uni.showToast({
      title: '请检查表单输入',
      icon: 'none'
    })
    return
  }
  
  try {
    formLoading.value = true
    
    const submitData: CreateTaskData = {
      title: formData.title.trim(),
      description: formData.description.trim(),
      priority: formData.priority,
      tags: formData.tags
    }
    
    if (formMode.value === 'create') {
      // 创建新任务
      const newTask = await taskService.createTask(submitData)
      tasks.value.unshift(newTask)
      
      uni.showToast({
        title: '创建成功',
        icon: 'success'
      })
    } else {
      // 更新现有任务
      if (!currentTask.value) return
      
      const updatedTask = await taskService.updateTask(currentTask.value.id, submitData)
      
      // 更新本地数据
      const taskIndex = tasks.value.findIndex(t => t.id === currentTask.value!.id)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = updatedTask
      }
      
      uni.showToast({
        title: '更新成功',
        icon: 'success'
      })
    }
    
    // 刷新统计信息
    await refreshStats()
    
    // 隐藏表单
    hideForm()
  } catch (error) {
    console.error('保存任务失败:', error)
    uni.showToast({
      title: '保存失败，请重试',
      icon: 'none'
    })
  } finally {
    formLoading.value = false
  }
}

// 初始化页面数据
const initPageData = async (): Promise<void> => {
  try {
    globalLoading.value = true
    
    // 初始化示例数据（仅在首次使用时）
    await taskService.initSampleData()
    
    // 加载任务列表
    await loadTasks()
  } catch (error) {
    console.error('初始化页面数据失败:', error)
    uni.showToast({
      title: '初始化失败，请重试',
      icon: 'none'
    })
  } finally {
    globalLoading.value = false
  }
}

// 页面生命周期
onMounted(() => {
  initPageData()
})
</script>

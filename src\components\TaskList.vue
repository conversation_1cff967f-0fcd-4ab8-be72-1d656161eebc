<!--
  任务列表组件
  用于显示任务列表，支持筛选、排序、加载状态等功能
  包含任务卡片的渲染和各种交互事件的处理
-->

<template>
  <view class="task-list">
    <!-- 列表头部：筛选和排序控件 -->
    <view class="list-header" v-if="showHeader">
      <!-- 筛选器 -->
      <view class="filter-section">
        <text class="section-title">筛选条件</text>
        <view class="filter-controls">
          <!-- 状态筛选 -->
          <picker 
            :value="statusFilterIndex" 
            :range="statusOptions" 
            range-key="label"
            @change="handleStatusFilterChange"
          >
            <view class="filter-item">
              <text class="filter-label">状态：</text>
              <text class="filter-value">{{ currentStatusFilter }}</text>
            </view>
          </picker>

          <!-- 优先级筛选 -->
          <picker 
            :value="priorityFilterIndex" 
            :range="priorityOptions" 
            range-key="label"
            @change="handlePriorityFilterChange"
          >
            <view class="filter-item">
              <text class="filter-label">优先级：</text>
              <text class="filter-value">{{ currentPriorityFilter }}</text>
            </view>
          </picker>
        </view>
      </view>

      <!-- 排序器 -->
      <view class="sort-section">
        <text class="section-title">排序方式</text>
        <picker 
          :value="sortOptionIndex" 
          :range="sortOptions" 
          range-key="label"
          @change="handleSortChange"
        >
          <view class="sort-item">
            <text class="sort-label">{{ currentSortOption }}</text>
          </view>
        </picker>
      </view>

      <!-- 重置按钮 -->
      <button class="reset-btn" @click="handleResetFilters">
        重置筛选
      </button>
    </view>

    <!-- 搜索框 -->
    <view class="search-section" v-if="showSearch">
      <input 
        class="search-input"
        type="text"
        placeholder="搜索任务标题或描述..."
        :value="searchText"
        @input="handleSearchInput"
      />
    </view>

    <!-- 任务统计信息 -->
    <view class="stats-section" v-if="showStats && stats">
      <view class="stat-item">
        <text class="stat-number">{{ stats.total }}</text>
        <text class="stat-label">总计</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.pending }}</text>
        <text class="stat-label">待完成</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.inProgress }}</text>
        <text class="stat-label">进行中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.completed }}</text>
        <text class="stat-label">已完成</text>
      </view>
    </view>

    <!-- 加载状态 -->
    <view class="loading-section" v-if="loading">
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 任务列表内容 -->
    <view class="list-content" v-else>
      <!-- 空状态 -->
      <view class="empty-state" v-if="filteredTasks.length === 0">
        <text class="empty-title">暂无任务</text>
        <text class="empty-description">
          {{ hasFilters ? '没有符合筛选条件的任务' : '还没有创建任何任务' }}
        </text>
        <button class="create-btn" @click="handleCreateTask" v-if="!hasFilters">
          创建第一个任务
        </button>
      </view>

      <!-- 任务卡片列表 -->
      <view class="task-items" v-else>
        <TaskCard
          v-for="task in filteredTasks"
          :key="task.id"
          :task="task"
          :show-actions="showTaskActions"
          :compact="compactMode"
          @task-click="handleTaskClick"
          @status-change="handleStatusChange"
          @edit-task="handleEditTask"
          @delete-task="handleDeleteTask"
        />
      </view>

      <!-- 加载更多按钮 -->
      <view class="load-more-section" v-if="hasMore && !loading">
        <button class="load-more-btn" @click="handleLoadMore">
          加载更多
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * 任务列表组件的逻辑部分
 * 处理任务列表的显示、筛选、排序等功能
 */

import { ref, computed, watch } from 'vue'
import type { Task, TaskStatus, TaskPriority, TaskFilter, SortOption, TaskStats } from '@/types'
import { getStatusText, getPriorityText, filterTasks, sortTasks, debounce } from '@/utils'
import TaskCard from './TaskCard.vue'

// 定义组件接收的属性
interface Props {
  tasks: Task[]                    // 任务列表数据
  loading?: boolean               // 加载状态
  showHeader?: boolean            // 是否显示头部筛选器
  showSearch?: boolean            // 是否显示搜索框
  showStats?: boolean             // 是否显示统计信息
  showTaskActions?: boolean       // 是否显示任务操作按钮
  compactMode?: boolean          // 是否使用紧凑模式
  hasMore?: boolean              // 是否有更多数据
  stats?: TaskStats              // 统计信息
}

// 定义组件发出的事件
interface Emits {
  (e: 'task-select', task: Task): void                           // 任务选择事件
  (e: 'filter-change', filter: TaskFilter): void                // 筛选条件改变事件
  (e: 'sort-change', sortOption: SortOption): void              // 排序选项改变事件
  (e: 'search-change', searchText: string): void                // 搜索文本改变事件
  (e: 'status-change', taskId: string, status: TaskStatus): void // 任务状态改变事件
  (e: 'edit-task', task: Task): void                            // 编辑任务事件
  (e: 'delete-task', taskId: string): void                      // 删除任务事件
  (e: 'create-task'): void                                      // 创建任务事件
  (e: 'load-more'): void                                        // 加载更多事件
}

// 接收属性，设置默认值
const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showHeader: true,
  showSearch: true,
  showStats: true,
  showTaskActions: true,
  compactMode: false,
  hasMore: false
})

// 定义事件发射器
const emit = defineEmits<Emits>()

// 响应式数据
const searchText = ref('')                    // 搜索文本
const currentFilter = ref<TaskFilter>({})     // 当前筛选条件
const currentSort = ref<SortOption>({         // 当前排序选项
  field: 'createdAt',
  order: 'desc'
})

// 筛选选项配置
const statusOptions = [
  { value: '', label: '全部状态' },
  { value: 'pending', label: '待完成' },
  { value: 'in_progress', label: '进行中' },
  { value: 'completed', label: '已完成' }
]

const priorityOptions = [
  { value: '', label: '全部优先级' },
  { value: 'low', label: '低优先级' },
  { value: 'medium', label: '中优先级' },
  { value: 'high', label: '高优先级' }
]

const sortOptions = [
  { value: 'createdAt-desc', label: '创建时间（新到旧）' },
  { value: 'createdAt-asc', label: '创建时间（旧到新）' },
  { value: 'updatedAt-desc', label: '更新时间（新到旧）' },
  { value: 'priority-desc', label: '优先级（高到低）' },
  { value: 'title-asc', label: '标题（A到Z）' },
  { value: 'dueDate-asc', label: '截止日期（近到远）' }
]

// 计算属性：当前筛选器索引
const statusFilterIndex = computed(() => {
  return statusOptions.findIndex(option => option.value === currentFilter.value.status)
})

const priorityFilterIndex = computed(() => {
  return priorityOptions.findIndex(option => option.value === currentFilter.value.priority)
})

const sortOptionIndex = computed(() => {
  const sortKey = `${currentSort.value.field}-${currentSort.value.order}`
  return sortOptions.findIndex(option => option.value === sortKey)
})

// 计算属性：当前筛选和排序的显示文本
const currentStatusFilter = computed(() => {
  const option = statusOptions[statusFilterIndex.value]
  return option ? option.label : '全部状态'
})

const currentPriorityFilter = computed(() => {
  const option = priorityOptions[priorityFilterIndex.value]
  return option ? option.label : '全部优先级'
})

const currentSortOption = computed(() => {
  const option = sortOptions[sortOptionIndex.value]
  return option ? option.label : '创建时间（新到旧）'
})

// 计算属性：是否有筛选条件
const hasFilters = computed(() => {
  return !!(currentFilter.value.status || 
           currentFilter.value.priority || 
           searchText.value.trim())
})

// 计算属性：筛选和排序后的任务列表
const filteredTasks = computed(() => {
  let result = [...props.tasks]
  
  // 应用筛选条件
  const filter: TaskFilter = {
    ...currentFilter.value,
    searchText: searchText.value.trim() || undefined
  }
  
  if (Object.keys(filter).some(key => filter[key as keyof TaskFilter])) {
    result = filterTasks(result, filter)
  }
  
  // 应用排序
  result = sortTasks(result, currentSort.value)
  
  return result
})

// 防抖搜索函数
const debouncedSearch = debounce((text: string) => {
  emit('search-change', text)
}, 300)

/**
 * 处理搜索输入
 */
const handleSearchInput = (event: any): void => {
  const value = event.detail?.value || event.target?.value || ''
  searchText.value = value
  debouncedSearch(value)
}

/**
 * 处理状态筛选改变
 */
const handleStatusFilterChange = (event: any): void => {
  const index = event.detail.value
  const selectedOption = statusOptions[index]
  currentFilter.value.status = selectedOption.value as TaskStatus || undefined
  emit('filter-change', currentFilter.value)
}

/**
 * 处理优先级筛选改变
 */
const handlePriorityFilterChange = (event: any): void => {
  const index = event.detail.value
  const selectedOption = priorityOptions[index]
  currentFilter.value.priority = selectedOption.value as TaskPriority || undefined
  emit('filter-change', currentFilter.value)
}

/**
 * 处理排序改变
 */
const handleSortChange = (event: any): void => {
  const index = event.detail.value
  const selectedOption = sortOptions[index]
  const [field, order] = selectedOption.value.split('-')
  
  currentSort.value = {
    field: field as any,
    order: order as 'asc' | 'desc'
  }
  
  emit('sort-change', currentSort.value)
}

/**
 * 重置筛选条件
 */
const handleResetFilters = (): void => {
  searchText.value = ''
  currentFilter.value = {}
  currentSort.value = {
    field: 'createdAt',
    order: 'desc'
  }
  emit('filter-change', {})
  emit('sort-change', currentSort.value)
  emit('search-change', '')
}

/**
 * 处理任务点击
 */
const handleTaskClick = (task: Task): void => {
  emit('task-select', task)
}

/**
 * 处理任务状态改变
 */
const handleStatusChange = (taskId: string, status: TaskStatus): void => {
  emit('status-change', taskId, status)
}

/**
 * 处理编辑任务
 */
const handleEditTask = (task: Task): void => {
  emit('edit-task', task)
}

/**
 * 处理删除任务
 */
const handleDeleteTask = (taskId: string): void => {
  emit('delete-task', taskId)
}

/**
 * 处理创建任务
 */
const handleCreateTask = (): void => {
  emit('create-task')
}

/**
 * 处理加载更多
 */
const handleLoadMore = (): void => {
  emit('load-more')
}

// 监听筛选条件变化
watch(currentFilter, (newFilter) => {
  emit('filter-change', newFilter)
}, { deep: true })

// 监听排序选项变化
watch(currentSort, (newSort) => {
  emit('sort-change', newSort)
}, { deep: true })
</script>

<style lang="scss" scoped>
/**
 * 任务列表组件的样式
 */

.task-list {
  width: 100%;
}

/* 列表头部 */
.list-header {
  background-color: var(--bg-primary);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
}

.section-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
  display: block;
}

/* 筛选器部分 */
.filter-section {
  margin-bottom: var(--spacing-md);
}

.filter-controls {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    background-color: var(--border-light);
  }
}

.filter-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-right: var(--spacing-xs);
}

.filter-value {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

/* 排序器部分 */
.sort-section {
  margin-bottom: var(--spacing-md);
}

.sort-item {
  padding: var(--spacing-sm);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-small);
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    background-color: var(--border-light);
  }
}

.sort-label {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  font-weight: 500;
}

/* 重置按钮 */
.reset-btn {
  width: 100%;
  padding: var(--spacing-sm);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-small);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }
}

/* 搜索部分 */
.search-section {
  margin-bottom: var(--spacing-md);
}

.search-input {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  background-color: var(--bg-primary);
  transition: border-color var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
}

/* 统计部分 */
.stats-section {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background-color: var(--bg-primary);
  border-radius: var(--radius-medium);
  box-shadow: var(--shadow-light);
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* 加载状态 */
.loading-section {
  display: flex;
  justify-content: center;
  padding: var(--spacing-xxl);
}

.loading-text {
  color: var(--text-secondary);
  font-size: var(--font-size-md);
}

/* 列表内容 */
.list-content {
  width: 100%;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
}

.empty-title {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
}

.empty-description {
  font-size: var(--font-size-md);
  color: var(--text-tertiary);
  margin-bottom: var(--spacing-lg);
}

.create-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--text-white);
  border: none;
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast);

  &:hover {
    opacity: 0.8;
  }
}

/* 任务项目 */
.task-items {
  width: 100%;
}

/* 加载更多 */
.load-more-section {
  display: flex;
  justify-content: center;
  padding: var(--spacing-lg);
}

.load-more-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: var(--radius-medium);
  font-size: var(--font-size-md);
  cursor: pointer;
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filter-controls {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .stats-section {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .stat-item {
    min-width: calc(50% - var(--spacing-xs));
  }
}
</style>

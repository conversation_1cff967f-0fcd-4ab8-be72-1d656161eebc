/**
 * 工具函数文件
 * 包含整个应用中使用的通用工具函数
 * 这些函数是纯函数，不依赖于组件状态，可以在任何地方使用
 */

import { Task, TaskStatus, TaskPriority, TaskFilter, SortOption } from '@/types'

/**
 * 生成唯一ID
 * 使用时间戳和随机数生成唯一标识符
 * @returns {string} 唯一ID字符串
 */
export function generateId(): string {
  // 使用当前时间戳和随机数确保ID的唯一性
  return Date.now().toString(36) + Math.random().toString(36).substr(2)
}

/**
 * 格式化日期
 * 将Date对象格式化为易读的字符串
 * @param {Date} date - 要格式化的日期
 * @param {string} format - 格式化模式，默认为 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
  if (!date || !(date instanceof Date)) {
    return ''
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  // 根据格式参数返回不同的格式
  switch (format) {
    case 'YYYY-MM-DD':
      return `${year}-${month}-${day}`
    case 'YYYY-MM-DD HH:mm':
      return `${year}-${month}-${day} ${hours}:${minutes}`
    case 'MM-DD':
      return `${month}-${day}`
    case 'HH:mm':
      return `${hours}:${minutes}`
    default:
      return `${year}-${month}-${day}`
  }
}

/**
 * 计算相对时间
 * 计算给定日期与当前时间的相对差值
 * @param {Date} date - 目标日期
 * @returns {string} 相对时间描述
 */
export function getRelativeTime(date: Date): string {
  if (!date || !(date instanceof Date)) {
    return ''
  }

  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  if (diffDays > 0) {
    return `${diffDays}天前`
  } else if (diffHours > 0) {
    return `${diffHours}小时前`
  } else if (diffMinutes > 0) {
    return `${diffMinutes}分钟前`
  } else {
    return '刚刚'
  }
}

/**
 * 获取任务状态的中文描述
 * @param {TaskStatus} status - 任务状态
 * @returns {string} 中文描述
 */
export function getStatusText(status: TaskStatus): string {
  const statusMap = {
    [TaskStatus.PENDING]: '待完成',
    [TaskStatus.IN_PROGRESS]: '进行中',
    [TaskStatus.COMPLETED]: '已完成'
  }
  return statusMap[status] || '未知状态'
}

/**
 * 获取任务优先级的中文描述
 * @param {TaskPriority} priority - 任务优先级
 * @returns {string} 中文描述
 */
export function getPriorityText(priority: TaskPriority): string {
  const priorityMap = {
    [TaskPriority.LOW]: '低',
    [TaskPriority.MEDIUM]: '中',
    [TaskPriority.HIGH]: '高'
  }
  return priorityMap[priority] || '未知优先级'
}

/**
 * 获取任务状态对应的颜色
 * @param {TaskStatus} status - 任务状态
 * @returns {string} 颜色值
 */
export function getStatusColor(status: TaskStatus): string {
  const colorMap = {
    [TaskStatus.PENDING]: '#FF9500',    // 橙色
    [TaskStatus.IN_PROGRESS]: '#007AFF', // 蓝色
    [TaskStatus.COMPLETED]: '#34C759'    // 绿色
  }
  return colorMap[status] || '#8E8E93'
}

/**
 * 获取任务优先级对应的颜色
 * @param {TaskPriority} priority - 任务优先级
 * @returns {string} 颜色值
 */
export function getPriorityColor(priority: TaskPriority): string {
  const colorMap = {
    [TaskPriority.LOW]: '#34C759',    // 绿色
    [TaskPriority.MEDIUM]: '#FF9500', // 橙色
    [TaskPriority.HIGH]: '#FF3B30'    // 红色
  }
  return colorMap[priority] || '#8E8E93'
}

/**
 * 任务筛选函数
 * 根据筛选条件过滤任务列表
 * @param {Task[]} tasks - 任务列表
 * @param {TaskFilter} filter - 筛选条件
 * @returns {Task[]} 筛选后的任务列表
 */
export function filterTasks(tasks: Task[], filter: TaskFilter): Task[] {
  return tasks.filter(task => {
    // 按状态筛选
    if (filter.status && task.status !== filter.status) {
      return false
    }

    // 按优先级筛选
    if (filter.priority && task.priority !== filter.priority) {
      return false
    }

    // 按搜索文本筛选（标题和描述）
    if (filter.searchText) {
      const searchText = filter.searchText.toLowerCase()
      const titleMatch = task.title.toLowerCase().includes(searchText)
      const descMatch = task.description.toLowerCase().includes(searchText)
      if (!titleMatch && !descMatch) {
        return false
      }
    }

    // 按标签筛选
    if (filter.tags && filter.tags.length > 0) {
      const hasMatchingTag = filter.tags.some(tag => task.tags.includes(tag))
      if (!hasMatchingTag) {
        return false
      }
    }

    return true
  })
}

/**
 * 任务排序函数
 * 根据排序选项对任务列表进行排序
 * @param {Task[]} tasks - 任务列表
 * @param {SortOption} sortOption - 排序选项
 * @returns {Task[]} 排序后的任务列表
 */
export function sortTasks(tasks: Task[], sortOption: SortOption): Task[] {
  return [...tasks].sort((a, b) => {
    let aValue: any
    let bValue: any

    // 根据排序字段获取比较值
    switch (sortOption.field) {
      case 'createdAt':
        aValue = a.createdAt.getTime()
        bValue = b.createdAt.getTime()
        break
      case 'updatedAt':
        aValue = a.updatedAt.getTime()
        bValue = b.updatedAt.getTime()
        break
      case 'dueDate':
        aValue = a.dueDate ? a.dueDate.getTime() : 0
        bValue = b.dueDate ? b.dueDate.getTime() : 0
        break
      case 'priority':
        // 优先级排序：高 > 中 > 低
        const priorityOrder = { [TaskPriority.HIGH]: 3, [TaskPriority.MEDIUM]: 2, [TaskPriority.LOW]: 1 }
        aValue = priorityOrder[a.priority]
        bValue = priorityOrder[b.priority]
        break
      case 'title':
        aValue = a.title.toLowerCase()
        bValue = b.title.toLowerCase()
        break
      default:
        return 0
    }

    // 根据排序方向返回结果
    if (sortOption.order === 'asc') {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    } else {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    }
  })
}

/**
 * 防抖函数
 * 在指定时间内多次调用只执行最后一次
 * @param {Function} func - 要防抖的函数
 * @param {number} delay - 延迟时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: number | null = null

  return (...args: Parameters<T>) => {
    if (timeoutId !== null) {
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数
 * 在指定时间内最多执行一次
 * @param {Function} func - 要节流的函数
 * @param {number} delay - 间隔时间（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0

  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

/**
 * 深拷贝函数
 * 创建对象的深度副本
 * @param {T} obj - 要拷贝的对象
 * @returns {T} 深拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 本地存储工具
 * 封装uni.setStorageSync和uni.getStorageSync
 */
export const storage = {
  /**
   * 设置本地存储
   * @param {string} key - 存储键
   * @param {any} value - 存储值
   */
  set(key: string, value: any): void {
    try {
      uni.setStorageSync(key, JSON.stringify(value))
    } catch (error) {
      console.error('存储数据失败:', error)
    }
  },

  /**
   * 获取本地存储
   * @param {string} key - 存储键
   * @param {any} defaultValue - 默认值
   * @returns {any} 存储的值或默认值
   */
  get<T>(key: string, defaultValue: T | null = null): T | null {
    try {
      const value = uni.getStorageSync(key)
      return value ? JSON.parse(value) : defaultValue
    } catch (error) {
      console.error('获取存储数据失败:', error)
      return defaultValue
    }
  },

  /**
   * 删除本地存储
   * @param {string} key - 存储键
   */
  remove(key: string): void {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error('删除存储数据失败:', error)
    }
  },

  /**
   * 清空本地存储
   */
  clear(): void {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('清空存储数据失败:', error)
    }
  }
}

# 🚀 快速开始指南

## 1. 环境准备

### 必需软件
- **Node.js**: 版本 >= 16.0.0
- **npm** 或 **yarn**: 包管理器
- **HBuilderX** 或 **VS Code**: 开发工具

### 开发工具推荐
- **HBuilderX**: 官方推荐的 UniApp 开发工具
- **VS Code**: 配合 UniApp 插件使用

## 2. 项目启动

### 安装依赖
```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 开发运行

#### H5 开发 (推荐新手)
```bash
npm run dev:h5
```
然后在浏览器中打开 `http://localhost:3000` 查看效果

#### 微信小程序开发
```bash
npm run dev:mp-weixin
```
1. 运行命令后，项目会在 `dist/dev/mp-weixin` 目录生成小程序代码
2. 打开微信开发者工具
3. 导入项目，选择 `dist/dev/mp-weixin` 目录
4. 即可在微信开发者工具中预览和调试

#### 其他平台
```bash
# 支付宝小程序
npm run dev:mp-alipay

# 百度小程序
npm run dev:mp-baidu

# QQ小程序
npm run dev:mp-qq

# 头条小程序
npm run dev:mp-toutiao
```

## 3. 项目结构说明

```
src/
├── components/          # 🧩 组件库
│   ├── TaskCard.vue    # 任务卡片 - 显示单个任务
│   ├── TaskList.vue    # 任务列表 - 显示任务列表和筛选
│   └── TaskForm.vue    # 任务表单 - 创建/编辑任务
├── pages/              # 📱 页面
│   ├── index/          # 首页 - 任务管理主界面
│   └── task-detail/    # 详情页 - 任务详细信息
├── services/           # 🔧 服务层
│   └── taskService.ts  # 任务数据管理
├── styles/             # 🎨 样式
│   └── common.scss     # 全局样式和变量
├── types/              # 📝 类型定义
│   └── index.ts        # TypeScript 类型
├── utils/              # 🛠️ 工具函数
│   └── index.ts        # 通用工具
└── App.vue             # 🏠 应用根组件
```

## 4. 功能演示

### 基础功能
1. **创建任务**: 点击右上角 "新建任务" 按钮
2. **编辑任务**: 点击任务卡片上的 "编辑" 按钮
3. **删除任务**: 点击任务卡片上的 "删除" 按钮
4. **状态切换**: 点击任务卡片上的状态按钮

### 高级功能
1. **搜索任务**: 在搜索框中输入关键词
2. **筛选任务**: 使用状态和优先级筛选器
3. **排序任务**: 选择不同的排序方式
4. **查看详情**: 点击任务卡片查看详细信息

## 5. 代码学习路径

### 初学者路径
1. **先看类型定义** (`src/types/index.ts`)
   - 了解数据结构
   - 理解 TypeScript 类型系统

2. **再看工具函数** (`src/utils/index.ts`)
   - 学习常用工具函数
   - 理解函数式编程思想

3. **然后看组件** (`src/components/`)
   - 从简单的 TaskCard 开始
   - 理解 Vue3 Composition API
   - 学习组件通信

4. **最后看页面** (`src/pages/`)
   - 理解页面级组件
   - 学习状态管理
   - 理解数据流

### 进阶学习
1. **服务层** (`src/services/`)
   - 学习数据管理
   - 理解异步编程
   - 模拟 API 调用

2. **样式系统** (`src/styles/`)
   - 学习 SCSS 语法
   - 理解 CSS 变量
   - 掌握响应式设计

## 6. 常见问题

### Q: 如何添加新的任务字段？
A: 
1. 在 `src/types/index.ts` 中更新 `Task` 接口
2. 在 `src/components/TaskForm.vue` 中添加表单字段
3. 在 `src/components/TaskCard.vue` 中显示新字段

### Q: 如何修改主题颜色？
A: 
在 `src/styles/common.scss` 中修改 CSS 变量：
```scss
:root {
  --primary-color: #your-color;
}
```

### Q: 如何添加新页面？
A:
1. 在 `src/pages/` 中创建新页面目录
2. 在 `src/pages.json` 中注册页面路由
3. 使用 `uni.navigateTo()` 进行页面跳转

### Q: 如何部署到生产环境？
A:
```bash
# 构建 H5 版本
npm run build:h5

# 构建小程序版本
npm run build:mp-weixin
```

## 7. 学习资源

### 官方文档
- [UniApp 官方文档](https://uniapp.dcloud.io/)
- [Vue3 官方文档](https://v3.vuejs.org/)
- [TypeScript 官方文档](https://www.typescriptlang.org/)

### 推荐教程
- Vue3 Composition API 教程
- TypeScript 入门教程
- SCSS 语法教程
- UniApp 实战教程

## 8. 下一步

学完这个项目后，你可以尝试：

1. **添加新功能**
   - 任务分类
   - 任务提醒
   - 数据导出

2. **优化用户体验**
   - 添加动画效果
   - 优化加载状态
   - 添加手势操作

3. **集成后端**
   - 用户登录
   - 数据同步
   - 实时通知

4. **发布应用**
   - 打包发布到各个平台
   - 应用商店上架
   - 用户反馈收集

---

🎉 **祝你学习愉快！如果遇到问题，请查看代码注释或提交 Issue。**
